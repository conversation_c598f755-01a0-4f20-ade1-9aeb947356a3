# loadChannelInfo 方法实现文档

## 方法概述

`loadChannelInfo` 方法用于根据租户认证连接中的支付开关状态，组装路由查询参数，调用 `PaymentChannelFacade.getRoutingInfo` 方法获取路由信息，并将获取到的渠道信息填充回租户认证连接对象中。

## 核心功能

### 1. 支付场景识别
根据 `TenantAuthConnection` 中的开关状态，识别启用的支付场景：

- **小程序微信支付**：
  - 总开关：`appletWechatPaySwitch = 1`
  - 微信直连：`wechatDirectSwitch = 1`
  - 微信间连：`wechatIndirectSwitch = 1` 或 `wechatIndirectPluginSwitch = 1`

- **H5微信支付**：`h5WechatIndirectSwitch = 1`

- **H5支付宝支付**：`aliIndirectSwitch = 1`

### 2. 路由查询参数构建
为每个启用的支付场景构建 `PaymentRoutingQueryDTO`：

```java
PaymentRoutingQueryDTO query = new PaymentRoutingQueryDTO();
query.setTenantId(tenantId);
query.setBusinessLine(PaymentBusinessLineEnums.SAAS.getCode());
query.setSceneName(PaymentSceneEnum.APPLET_WECHAT_PAY.getSceneName());
query.setPlatform(PaymentSceneEnum.APPLET_WECHAT_PAY.getPlatform());
query.setPaymentMethod(PaymentSceneEnum.APPLET_WECHAT_PAY.getPaymentMethod());
```

### 3. 路由信息查询
调用 `PaymentChannelFacade.getRoutingInfo` 方法获取每个场景的路由信息。

### 4. 渠道信息填充
根据返回的路由信息，将渠道配置填充到 `TenantAuthConnection` 对象中：

#### 微信渠道
- `merchantNo` → `payMchid`
- `secret` → `paySecret`
- `certPath` → `payCertPath`

#### 汇付渠道
- `merchantNo` → `huifuId`
- `privateKey` → `secretKey`
- `publicKey` → `huifuPublicKey`

#### 智付渠道
- `merchantNo` → `dinMerchantNo`
- `privateKey` → `dinPrivateKey`
- `publicKey` → `dinPublicKey`
- `secret` → `dinSecret`

## 方法实现

### 主方法
```java
private void loadChannelInfo(TenantAuthConnection tenantAuthConnection) {
    // 1. 构建路由查询参数列表
    List<PaymentRoutingQueryDTO> routingQueries = buildRoutingQueries(tenantAuthConnection);
    
    // 2. 查询每个场景的路由信息
    for (PaymentRoutingQueryDTO queryDTO : routingQueries) {
        PaymentRoutingDTO routingInfo = paymentChannelFacade.getRoutingInfo(queryDTO);
        if (routingInfo != null) {
            // 3. 填充渠道信息
            fillChannelInfoFromRouting(tenantAuthConnection, routingInfo, queryDTO);
        }
    }
}
```

### 辅助方法

#### buildRoutingQueries
构建路由查询参数列表，根据开关状态识别启用的支付场景。

#### fillChannelInfoFromRouting
将路由信息填充到租户认证连接对象中，根据渠道名称判断渠道类型。

## 支付场景映射

| 开关组合 | 支付场景 | 场景名称 | 平台 | 支付方式 |
|---------|---------|---------|------|---------|
| appletWechatPaySwitch=1 + wechatDirectSwitch=1 | 小程序微信直连 | APPLET_WECHAT_PAY | APPLET | WECHAT |
| appletWechatPaySwitch=1 + wechatIndirectSwitch=1 | 小程序微信间连 | APPLET_WECHAT_PAY | APPLET | WECHAT |
| h5WechatIndirectSwitch=1 | H5微信支付 | H5_WECHAT_PAY | H5 | WECHAT |
| aliIndirectSwitch=1 | H5支付宝支付 | H5_ALI_PAY | H5 | ALIPAY |

## 错误处理

### 1. 异常捕获
每个路由查询都有独立的异常处理，单个场景查询失败不影响其他场景。

### 2. 日志记录
- **成功日志**：记录成功获取的路由信息
- **警告日志**：记录未获取到路由信息的情况
- **错误日志**：记录查询异常的详细信息

### 3. 容错机制
- 路由信息为空时跳过填充
- 渠道名称为空时跳过填充
- 单个场景失败不影响整体流程

## 使用示例

### 调用方式
```java
// 在 selectAuthorizer 方法中调用
TenantAuthConnection tenantAuthConnection = tenantAuthConnections.get(0);
loadChannelInfo(tenantAuthConnection);
TenantAuthVO tenantAuthVO = TenantAuthConvert.convert2AuthVo(tenantAuthConnection);
```

### 开关配置示例
```java
// 启用小程序微信直连
tenantAuthConnection.setAppletWechatPaySwitch(1);
tenantAuthConnection.setWechatDirectSwitch(1);

// 启用H5微信支付
tenantAuthConnection.setH5WechatIndirectSwitch(1);

// 启用H5支付宝支付
tenantAuthConnection.setAliIndirectSwitch(1);
```

## 日志输出示例

### 成功场景
```log
INFO - 开始加载租户支付渠道信息，租户ID: 12345
INFO - 租户12345启用了2个支付场景，开始查询路由信息
DEBUG - 添加小程序微信直连路由查询，租户ID: 12345
DEBUG - 添加H5微信支付路由查询，租户ID: 12345
INFO - 成功获取路由信息，租户ID: 12345, 场景: APPLET_WECHAT_PAY, 渠道: 微信直连
DEBUG - 填充渠道信息，租户ID: 12345, 场景: APPLET_WECHAT_PAY, 渠道: 微信直连
INFO - 完成加载租户支付渠道信息，租户ID: 12345
```

### 异常场景
```log
WARN - 租户12345没有启用任何支付场景，跳过路由信息查询
WARN - 未获取到路由信息，租户ID: 12345, 场景: H5_WECHAT_PAY
ERROR - 查询路由信息失败，租户ID: 12345, 场景: APPLET_WECHAT_PAY
```

## 测试覆盖

### 单元测试场景
1. **成功场景**：正常获取路由信息并填充
2. **租户不存在**：返回空结果
3. **多场景启用**：验证多个支付场景的处理
4. **无场景启用**：验证不调用路由查询
5. **路由查询异常**：验证异常处理不影响整体流程

### Mock 对象
- `PaymentChannelFacade.getRoutingInfo`：Mock 路由信息返回
- `TenantAuthConnectionDao.listByParam`：Mock 租户认证连接查询

## 注意事项

### 1. 性能考虑
- 每个支付场景都会调用一次路由查询RPC
- 建议在业务低峰期进行批量查询
- 可考虑添加缓存机制优化性能

### 2. 数据一致性
- 路由信息的实时性依赖中央服务
- 本地填充的信息可能存在延迟
- 建议定期同步最新的路由信息

### 3. 扩展性
- 新增支付场景时需要更新 `buildRoutingQueries` 方法
- 新增渠道类型时需要更新 `fillChannelInfoFromRouting` 方法
- 建议使用策略模式优化渠道信息填充逻辑

## 后续优化建议

1. **批量查询优化**：支持一次RPC调用查询多个场景的路由信息
2. **缓存机制**：添加路由信息缓存，减少RPC调用
3. **配置化**：将支付场景映射关系配置化，提高可维护性
4. **监控告警**：添加路由查询成功率监控和异常告警
