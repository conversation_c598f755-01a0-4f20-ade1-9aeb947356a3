# 支付渠道多渠道支持重构文档

## 问题描述

原有的 `TenantAuthConnectionServiceImpl.saveOrUpdateAuth` 方法存在以下问题：
1. 不能支持多渠道保存（微信原生和间连）
2. `PaymentChannelFacade.saveChannelConfig` 方法逻辑混乱，试图在一个方法中处理所有渠道类型
3. 缺乏对不同渠道的独立配置和验证

## 解决方案

### 1. 重构 PaymentChannelFacade

#### 新增 ChannelConfig 内部类
```java
@Data
public static class ChannelConfig {
    private Long tenantId;
    private String channelName;
    private String merchantNo;
    private String privateKey;
    private String publicKey;
    private String secret;
    private String certPath;
    private List<PaymentSceneConfigReq> sceneConfigs;
}
```

#### 新增多渠道支持方法
- `saveMultiChannelConfigs(TenantAuthServiceDTO authDto)`: 主入口方法，识别并保存多个渠道
- `saveChannelConfig(ChannelConfig channelConfig)`: 保存单个渠道配置
- `identifyEnabledChannels(TenantAuthServiceDTO authDto)`: 识别启用的渠道
- `buildWechatDirectConfig(TenantAuthServiceDTO authDto)`: 构建微信直连配置
- `buildIndirectChannelConfig(TenantAuthServiceDTO authDto)`: 构建间连渠道配置

### 2. 重构 TenantAuthConnectionServiceImpl

修改 `saveOrUpdateAuth` 方法：
- 使用新的 `saveMultiChannelConfigs` 方法替代原有的单一渠道保存
- 增强异常处理和日志记录
- 保持原有的参数验证逻辑

## 支持的渠道类型

### 1. 微信直连
- **识别条件**: `wechatDirectSwitch = 1`
- **必需参数**: `payMchid`, `paySecret`, `payCertPath`
- **渠道名称**: "微信直连"

### 2. 间连渠道

#### 汇付渠道
- **识别条件**: 任一间连开关启用 + `indirectOnlineChannel = 1`
- **必需参数**: `huifuId`, `secretKey`, `huifuPublicKey`
- **渠道名称**: 从 `PaymentDictionaryEnums.ChannelName.HUI_FU` 获取

#### 智付渠道
- **识别条件**: 任一间连开关启用 + `indirectOnlineChannel = 2`
- **必需参数**: `dinMerchantNo`, `dinPrivateKey`, `dinPublicKey`, `dinSecret`
- **渠道名称**: 从 `PaymentDictionaryEnums.ChannelName.DIN_PAY` 获取

## 支付场景映射

### 小程序微信支付
- **开关**: `appletWechatPaySwitch = 1`
- **场景**: `PaymentSceneEnum.APPLET_WECHAT_PAY`

### H5微信支付
- **开关**: `h5WechatIndirectSwitch = 1`
- **场景**: `PaymentSceneEnum.H5_WECHAT_PAY`

### H5支付宝支付
- **开关**: `aliIndirectSwitch = 1`
- **场景**: `PaymentSceneEnum.H5_ALI_PAY`

## 关键特性

### 1. 渠道独立性
每个启用的渠道都会独立调用中央服务进行配置保存，确保渠道间的配置不会相互影响。

### 2. 参数验证
在构建渠道配置时，会验证每个渠道的必需参数是否完整，不完整的配置会被跳过并记录警告日志。

### 3. 异常处理
- 如果某个渠道配置保存失败，会抛出包含具体渠道信息的异常
- 保持事务一致性，确保要么全部成功，要么全部回滚

### 4. 日志记录
- 详细记录每个渠道的识别、构建和保存过程
- 包含租户ID、渠道名称、渠道ID等关键信息

## 向后兼容性

保留了原有的 `saveChannelConfig(TenantAuthServiceDTO authDto)` 方法，标记为兼容旧版本，但建议使用新的多渠道方法。

## 测试覆盖

### 单元测试
- `PaymentChannelFacadeTest`: 测试多渠道配置保存逻辑
- `TenantAuthConnectionServiceImplTest`: 测试服务层集成逻辑

### 测试场景
1. 只启用微信直连
2. 只启用间连渠道（汇付/智付）
3. 同时启用多个渠道
4. 没有启用任何渠道
5. 存在现有连接的更新场景

## 使用示例

```java
// 创建租户认证服务DTO
TenantAuthServiceDTO dto = new TenantAuthServiceDTO();
dto.setTenantId(12345L);

// 启用微信直连
dto.setWechatDirectSwitch(PaymentMethodSwitchEnum.ENABLED.getCode());
dto.setPayMchid("wx_merchant_123");
dto.setPaySecret("wx_secret_123");
dto.setPayCertPath("/path/to/cert");

// 启用汇付间连
dto.setWechatIndirectSwitch(PaymentMethodSwitchEnum.ENABLED.getCode());
dto.setIndirectOnlineChannel(IndirectChannelTypeEnum.HUI_FU.getCode());
dto.setHuifuId("huifu_merchant_123");
dto.setSecretKey("huifu_private_key");
dto.setHuifuPublicKey("huifu_public_key");

// 启用支付场景
dto.setAppletWechatPaySwitch(PaymentMethodSwitchEnum.ENABLED.getCode());

// 保存配置
LoginContextInfoDTO loginContext = new LoginContextInfoDTO();
tenantAuthConnectionService.saveOrUpdateAuth(dto, loginContext);
```

## 注意事项

1. **参数完整性**: 确保每个启用渠道的必需参数都已正确设置
2. **渠道冲突**: 避免同时启用相互冲突的渠道配置
3. **异常处理**: 调用方需要处理可能抛出的 `BizException`
4. **日志监控**: 关注日志中的警告和错误信息，及时处理配置问题

## 后续优化建议

1. **配置验证增强**: 可以考虑添加更严格的参数格式验证
2. **渠道优先级**: 如果需要，可以添加渠道优先级逻辑
3. **配置缓存**: 对于频繁访问的配置，可以考虑添加缓存机制
4. **监控指标**: 添加渠道配置成功率等监控指标
