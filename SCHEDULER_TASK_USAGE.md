# SchedulerX 渠道初始化任务使用说明

## 任务概述

`ChannelInitSchedulerXTask` 是一个用于初始化历史存量支付配置数据的 SchedulerX 定时任务。该任务会扫描 `tenant_auth_connection` 表中的历史数据，识别有效的微信直连和汇付间连配置，并通过 RPC 接口初始化到中央支付服务。

## 任务特性

- **继承 XianMuJavaProcessorV2**：遵循项目规范
- **RPC 幂等性**：依赖中央服务的幂等性，避免重复初始化
- **分页处理**：支持大数据量的安全分批处理
- **灵活参数**：支持全量处理和指定租户处理
- **安全循环**：避免无限循环的风险

## 任务参数

### 参数格式
任务参数使用 JSON 格式，支持以下字段：

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| tenantIds | String | 否 | 租户ID列表，多个用逗号分隔 | "12345,67890,11111" |
| batchSize | Integer | 否 | 批处理大小，默认50 | 100 |

### 参数示例

```json
// 1. 处理全部租户
{}

// 2. 处理指定租户（单个）
{"tenantIds": "12345"}

// 3. 处理指定租户（多个）
{"tenantIds": "12345,67890,11111"}

// 4. 自定义批处理大小
{"tenantIds": "12345,67890", "batchSize": 100}

// 5. 处理全部租户，自定义批大小
{"batchSize": 200}
```

## 配置识别规则

### 微信直连配置
需要同时满足以下条件：
- `wechat_direct_switch = 1`（微信直连开关开启）
- `pay_mchid` 不为空（微信商户号）
- `pay_secret` 不为空（微信支付密钥）
- `pay_cert_path` 不为空（微信支付证书路径）

### 汇付间连配置
需要同时满足以下条件：
- 任一间连开关开启：
  - `wechat_indirect_switch = 1` 或
  - `wechat_indirect_plugin_switch = 1` 或
  - `h5_wechat_indirect_switch = 1`
- 汇付相关参数不为空：
  - `huifuid` 不为空（汇付商户ID）
  - `huifu_public_key` 不为空（汇付公钥）
  - `public_key` 不为空（公钥）
  - `secret_key` 不为空（私钥）

## 执行流程

1. **参数解析**：解析任务参数，提取租户ID列表和批处理大小
2. **数据查询**：根据参数查询需要处理的支付配置数据
3. **分页处理**：按批大小分页处理，避免内存溢出
4. **配置识别**：识别每个配置中的有效支付渠道
5. **RPC 调用**：调用 `PaymentChannelFacade.saveMultiChannelConfigs` 初始化配置
6. **结果统计**：统计处理结果并记录日志

## 日志说明

### 关键日志示例

```log
// 任务开始
INFO - 开始执行渠道初始化定时任务，任务ID: 1001, 参数: {"tenantIds": "12345,67890"}

// 参数解析
INFO - 解析到2个指定租户ID: [12345, 67890]

// 批次处理
INFO - 处理第1批支付配置数据，数量: 50

// 配置处理
INFO - 处理租户支付配置，configId: 123, tenantId: 12345
INFO - 租户12345微信直连渠道初始化成功，channelIds: [1001]
INFO - 租户12345汇付间连渠道初始化成功，channelIds: [1002]

// 任务完成
INFO - 渠道初始化任务执行完成，处理完成：总数100，成功95，跳过3，失败2
```

### 错误日志示例

```log
// 配置处理失败
ERROR - 租户12345微信直连渠道初始化失败
ERROR - 处理租户支付配置失败，configId: 123, tenantId: 12345

// 任务执行异常
ERROR - 渠道初始化定时任务执行异常，任务ID: 1001
```

## 监控告警

### 告警触发条件
- 任务执行失败（返回 `ProcessResult.success = false`）
- 出现 ERROR 级别日志
- 处理失败数量超过阈值

### 关键监控指标
- 任务执行成功率
- 配置处理成功率
- 任务执行耗时
- 处理的配置数量

## 注意事项

### 1. 数据安全
- 任务依赖 RPC 接口的幂等性，多次执行不会产生重复数据
- 建议在测试环境充分验证后再在生产环境执行

### 2. 性能考虑
- 默认批处理大小为 50，可根据实际情况调整
- 大量数据处理时建议在业务低峰期执行
- 指定租户处理比全量处理更高效

### 3. 错误处理
- 单个配置处理失败不会影响其他配置
- 失败的配置会记录详细错误信息
- 建议关注错误日志并及时处理

### 4. 参数验证
- 无效的租户ID会被自动过滤并记录警告日志
- 空的 `tenantIds` 参数等同于处理全部租户
- 批处理大小必须大于 0，否则使用默认值

## 使用建议

### 首次执行
1. 先在测试环境执行，验证功能正确性
2. 使用小批量数据测试（指定少量租户ID）
3. 观察日志输出，确认处理逻辑正确

### 生产环境执行
1. 建议在业务低峰期执行
2. 可以分批执行，先处理部分租户
3. 密切监控任务执行状态和错误日志
4. 执行完成后验证数据正确性

### 故障处理
1. 如果任务执行失败，检查错误日志确定原因
2. 可以重新执行任务，RPC 接口的幂等性保证数据安全
3. 如果是数据问题，修复数据后重新执行
4. 如果是系统问题，修复系统后重新执行
