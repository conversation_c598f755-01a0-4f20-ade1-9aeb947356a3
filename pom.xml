<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.cosfo</groupId>
    <artifactId>cosfo-oms</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>cosfo-oms</name>
    <description>cosfo-oms</description>

    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <spring-boot.version>2.3.7.RELEASE</spring-boot.version>
        <qiniu.version>[7.2.0, 7.2.99]</qiniu.version>
        <swagger.version>2.7.0</swagger.version>
        <gauva.version>28.2-jre</gauva.version>
        <commons-lang3>3.12.0</commons-lang3>
        <mysql.version>8.0.22</mysql.version>
        <jwt>0.9.1</jwt>
        <hutool.version>5.7.22</hutool.version>
        <smart-doc.version>2.5.2</smart-doc.version>
        <erp-client.version>1.1.9-RELEASE</erp-client.version>
        <org.mapstruct.version>1.5.3.Final</org.mapstruct.version>
        <xianmu-log-support.version>1.0.14-RELEASE</xianmu-log-support.version>
        <xianmu-dubbo-support.version>1.0.9</xianmu-dubbo-support.version>
        <xianmu-common.version>1.1.5-RELEASE</xianmu-common.version>
        <org.mapstruct.version>1.5.3.Final</org.mapstruct.version>
        <rocket-mq.version>1.2.1</rocket-mq.version>
        <authentication-sdk.version>1.1.13</authentication-sdk.version>
        <authentication-client.version>1.1.22</authentication-client.version>
        <usercenter-client.version>1.2.5</usercenter-client.version>
        <wms-client.version>1.2.5-RELEASE</wms-client.version>
        <nacos-config.version>0.2.12</nacos-config.version>
        <item.client.version>1.0.31-RELEASE</item.client.version>
        <cosfo-manage-client.version>1.4.0-RELEASE</cosfo-manage-client.version>
        <redisson.version>3.11.1</redisson.version>
        <xianmu-robot-util.version>1.0.2</xianmu-robot-util.version>
        <order-center-client.version>1.4.5-RELEASE</order-center-client.version>
        <cosfo-common.version>1.0.6</cosfo-common.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>ofc-client</artifactId>
            <version>1.2.4-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>summerfarm-wnc-client</artifactId>
            <version>1.2.6-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-mybatis-interceptor-support</artifactId>
            <version>1.0.2-RELEASE</version>
        </dependency>


        <dependency>
            <groupId>net.summerfarm.wms</groupId>
            <artifactId>summerfarm-wms-client</artifactId>
            <version>${wms-client.version}</version>
        </dependency>

        <!-- auth 服务依赖-->
        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>authentication-sdk</artifactId>
            <version>${authentication-sdk.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.elasticsearch</groupId>
                    <artifactId>elasticsearch</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.rocketmq</groupId>
                    <artifactId>rocketmq-spring-boot-starter</artifactId>
                </exclusion>

                <exclusion>
                    <groupId>org.elasticsearch.client</groupId>
                    <artifactId>elasticsearch-rest-client</artifactId>
                </exclusion>

                <exclusion>
                    <groupId>org.elasticsearch.client</groupId>
                    <artifactId>elasticsearch-rest-high-level-client</artifactId>
                </exclusion>
                <!-- org/mybatis/mybatis/3.5.0 -->
                <exclusion>
                    <groupId>org.mybatis</groupId>
                    <artifactId>mybatis</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>authentication-client</artifactId>
            <version>${authentication-client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.mybatis</groupId>
                    <artifactId>mybatis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baomidou</groupId>
                    <artifactId>mybatis-plus-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- dependencyManagement中配置版本号，在core工程中配置依赖 -->
        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-dubbo-support</artifactId>
            <version>${xianmu-dubbo-support.version}</version>
        </dependency>
        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>erp-client</artifactId>
            <version>${erp-client.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-registry-nacos</artifactId>
            <version>2.7.15</version>
        </dependency>
        <!-- 鲜沐rpc服务-->
        <dependency>
            <groupId>net.manage.client</groupId>
            <artifactId>manage-client</artifactId>
            <version>1.0.7-RELEASE</version>
        </dependency>
        <!--消息中心-->
        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>message-client</artifactId>
            <version>1.0-RELEASE</version>
        </dependency>
        <!--  七牛上传SDK  -->
        <dependency>
            <groupId>com.qiniu</groupId>
            <artifactId>qiniu-java-sdk</artifactId>
            <version>${qiniu.version}</version>
            <!--      <exclusions>-->
            <!--        <exclusion>-->
            <!--          <groupId>com.squareup.okhttp3</groupId>-->
            <!--          <artifactId>okhttp</artifactId>-->
            <!--        </exclusion>-->
            <!--      </exclusions>-->
        </dependency>

        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-log-support</artifactId>
            <version>${xianmu-log-support.version}</version>
        </dependency>

        <!-- 线程池调用 日志traceId跟踪 -->
        <dependency>
            <groupId>com.alibaba.arms.apm</groupId>
            <artifactId>arms-sdk</artifactId>
            <version>1.7.5</version>
        </dependency>


        <dependency>
            <groupId>org.icepear.echarts</groupId>
            <artifactId>echarts-java</artifactId>
            <version>1.0.6</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-simple</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
            <version>1.4</version>
        </dependency>

        <!-- EasyExcel依赖包-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.1.0</version>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${org.mapstruct.version}</version>
        </dependency>

        <!-- weichat -->
        <!--解析xml工具xstream-->
        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
            <version>1.4.4</version>
        </dependency>

        <!--    核心依赖模块    -->
        <dependency>
            <groupId>net.xianmu.common</groupId>
            <artifactId>xianmu-common</artifactId>
            <!--      根据实际版本修改，线上禁止SNAPSHOT版本     -->
            <version>${xianmu-common.version}</version>
        </dependency>

        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>common-client</artifactId>
            <version>1.0.15-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>xianmu-download-support</artifactId>
            <version>1.0.4</version>
        </dependency>

        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-oss-support</artifactId>
            <version>1.0.7</version>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.12</version>
        </dependency>

        <!--  emoji  -->
        <dependency>
            <groupId>com.vdurmont</groupId>
            <artifactId>emoji-java</artifactId>
            <version>4.0.0</version>
        </dependency>
        <!-- weichat-->

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.version}</version>
        </dependency>

        <!-- Saas 和 鲜沐二方库-->
        <dependency>
            <groupId>com.cosfo.summerfarm</groupId>
            <artifactId>saas-to-summerfarm</artifactId>
            <version>1.5.5-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>${commons-lang3}</version>
        </dependency>

        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>cosfo-common</artifactId>
            <version>1.0.4</version>
        </dependency>

        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>sf-mall-manage-client</artifactId>
            <version>1.2.8-GEORGE-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>summerfarm-payment-sdk</artifactId>
            <version>1.1.0-SNAPSHOT</version>
        </dependency>

        <!--  springboot 核心依赖包  -->
        <!--    <dependency>-->
        <!--      <groupId>org.springframework.boot</groupId>-->
        <!--      <artifactId>spring-boot-starter-data-jpa</artifactId>-->
        <!--    </dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.lettuce</groupId>
                    <artifactId>lettuce-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-task-support</artifactId>
            <version>1.0.5</version>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
            <version>${redisson.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis-reactive</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-quartz</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${org.mapstruct.version}</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.5.1</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>3.5.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-engine-core</artifactId>
            <version>2.2</version>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.alibaba/druid-spring-boot-starter -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>1.2.8</version>
        </dependency>

        <!--   分页插件     -->
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
            <version>1.4.1</version>
        </dependency>

        <!--https://gitee.com/sunyurepository/ApplicationPower-->
        <dependency>
            <groupId>com.github.shalousun</groupId>
            <artifactId>smart-doc</artifactId>
            <version>${smart-doc.version}</version>
        </dependency>

        <dependency>
            <groupId>com.github.shalousun</groupId>
            <artifactId>smart-doc-maven-plugin</artifactId>
            <version>${smart-doc.version}</version>
        </dependency>

        <!-- rocket mq -->
        <!--    <dependency>-->
        <!--      <groupId>org.apache.rocketmq</groupId>-->
        <!--      <artifactId>rocketmq-spring-boot-starter</artifactId>-->
        <!--      <version>2.1.1</version>-->
        <!--    </dependency>-->
        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-rocketmq-support</artifactId>
            <version>${rocket-mq.version}</version>
        </dependency>

        <!--  七牛上传SDK  -->
        <dependency>
            <groupId>com.qiniu</groupId>
            <artifactId>qiniu-java-sdk</artifactId>
            <version>${qiniu.version}</version>
        </dependency>

        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>oms-client</artifactId>
            <version>1.0.7-RELEASE</version>
        </dependency>

        <!--    用户中心-->
        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>usercenter-client</artifactId>
            <version>${usercenter-client.version}</version>
        </dependency>
        <dependency>
            <groupId>net.xianmu.common</groupId>
            <artifactId>xianmu-robot-util</artifactId>
            <version>${xianmu-robot-util.version}</version>
        </dependency>

        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>order-center-client</artifactId>
            <version>${order-center-client.version}</version>
        </dependency>

        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>goods-center-client</artifactId>
            <version>1.1.3-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>item-center-client</artifactId>
            <version>${item.client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>nacos-config-spring-boot-starter</artifactId>
            <version>${nacos-config.version}</version>
        </dependency>
        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>cosfo-manage-client</artifactId>
            <version>${cosfo-manage-client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>cosfo-common</artifactId>
            <version>${cosfo-common.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>easyexcel</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.aliyun.schedulerx</groupId>
                <artifactId>schedulerx2-spring-boot-starter</artifactId>
                <version>1.9.2</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>1.18.16</version>
                        </path>
                        <!-- This is needed when using Lombok 1.18.16 and above -->
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>0.2.0</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                        <!-- other annotation processors -->
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.3.7.RELEASE</version>
                <configuration>
                    <mainClass>com.cosfo.oms.CosfoOmsApplication</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>com.github.shalousun</groupId>
                <artifactId>smart-doc-maven-plugin</artifactId>
                <version>${smart-doc.version}</version>
                <configuration>
                    <includes>
                        <!--格式为：groupId:artifactId;参考如下-->
                        <!--也可以支持正则式如：com.alibaba:.* -->
                        <include>com.cosfo.oms.*.controller:.*</include>
                        <include>com.github.shalousun:.*</include>
                    </includes>
                    <!--指定生成文档的使用的配置文件-->
                    <configFile>./src/main/resources/smart-doc.json</configFile>
                    <!--指定项目名称-->
                    <projectName>测试</projectName>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>html</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
