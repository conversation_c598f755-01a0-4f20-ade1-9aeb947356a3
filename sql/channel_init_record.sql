-- 渠道初始化记录表
CREATE TABLE `channel_init_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `auth_connection_id` bigint(20) NOT NULL COMMENT '租户认证连接ID',
  `channel_type` varchar(50) NOT NULL COMMENT '渠道类型：WECHAT_DIRECT-微信直连，HUIFU_INDIRECT-汇付间连',
  `channel_id` bigint(20) DEFAULT NULL COMMENT '中央服务返回的渠道ID',
  `init_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '初始化状态：0-待初始化，1-初始化成功，2-初始化失败',
  `init_time` datetime DEFAULT NULL COMMENT '初始化时间',
  `error_message` text COMMENT '错误信息',
  `retry_count` int(11) NOT NULL DEFAULT '0' COMMENT '重试次数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tenant_auth_channel` (`tenant_id`,`auth_connection_id`,`channel_type`) COMMENT '租户+认证连接+渠道类型唯一索引',
  KEY `idx_tenant_id` (`tenant_id`) COMMENT '租户ID索引',
  KEY `idx_init_status` (`init_status`) COMMENT '初始化状态索引',
  KEY `idx_create_time` (`create_time`) COMMENT '创建时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='渠道初始化记录表';
