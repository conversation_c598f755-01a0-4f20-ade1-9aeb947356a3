package com.cosfo.oms.bill.model.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import net.xianmu.common.input.BasePageInput;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class PrepaymentAccountQueryDTO extends BasePageInput {

    private LocalDateTime date;

    /**
     * 付款人
     */
    private Long payee;

    /**
     * 商城名称
     */
    private String shopName;

    /**
     * 商城id
     */
    private Long tenantId;
}
