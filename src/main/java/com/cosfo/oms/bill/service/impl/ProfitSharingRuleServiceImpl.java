package com.cosfo.oms.bill.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.cosfo.oms.bill.mapper.BillProfitSharingRuleMapper;
import com.cosfo.oms.bill.model.dto.ProfitSharingRuleDTO;
import com.cosfo.oms.bill.model.po.BillProfitSharingRule;
import com.cosfo.oms.bill.model.vo.ProfitSharingRuleVO;
import com.cosfo.oms.bill.service.ProfitSharingRuleService;
import com.cosfo.oms.common.context.*;
import com.cosfo.oms.hf.dao.HuiFuAccountDao;
import com.cosfo.oms.hf.model.po.HuiFuAccount;
import com.cosfo.oms.product.model.vo.ProductAgentSkuFeeRuleDetailVO;
import com.cosfo.oms.product.model.vo.ProductAgentSkuFeeRuleVO;
import com.cosfo.oms.product.service.ProductAgentSkuFeeRuleService;
import com.cosfo.oms.tenant.dao.TenantAuthConnectionDao;
import com.cosfo.oms.tenant.event.TenantChangeEvent;
import com.cosfo.oms.tenant.model.dto.TenantAuthConnQueryDTO;
import com.cosfo.oms.tenant.model.po.TenantAuthConnection;
import com.cosfo.oms.tenant.model.vo.TenantVO;
import com.cosfo.oms.tenant.service.TenantService;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ParamsException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ProfitSharingRuleServiceImpl implements ProfitSharingRuleService {

    @Value("${tenant.ftTenantId}")
    private Long ftTenantId;

    @Value("${tenant.xmTenantId}")
    private Long xmTenantId;

    @Resource
    private BillProfitSharingRuleMapper mapper;
    @Resource
    @Lazy
    private ProductAgentSkuFeeRuleService productAgentSkuFeeRuleService;
    @Resource
    @Lazy
    private TenantService tenantService;
    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;
    @Resource
    private HuiFuAccountDao huiFuAccountDao;
    @Resource
    private TenantAuthConnectionDao tenantAuthConnectionDao;


    @Override
    public List<ProfitSharingRuleVO> ruleList(Long tenantId) {
        List<ProfitSharingRuleVO> result = new ArrayList<>();
        ArrayList<Integer> profitSharingDeliveryTypes = Lists.newArrayList(ProfitSharingDeliveryTypeEnums.BRAND_DELIVERY.getType(), ProfitSharingDeliveryTypeEnums.THIRD_DELIVERY.getType());
        List<BillProfitSharingRule> list = mapper.selectByTenantIdAndDeliveryTypes(tenantId, profitSharingDeliveryTypes);

        if (CollectionUtils.isEmpty(list)) {
            return initEmptyRuleList(tenantId);
        }
        List<Long> accountIds = list.stream().map(BillProfitSharingRule::getAccountId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(accountIds)) {
            return Collections.emptyList();
        }
        List<TenantVO> tenants = tenantService.selectByIds(accountIds);
        Map<Long, TenantVO> tenantMap = tenants.stream().collect(Collectors.toMap(TenantVO::getTenantId, Function.identity()));
        boolean normalReceiver = list.stream().anyMatch(el -> Objects.equals(el.getAccountType(), AccountTypeEnum.NORMAL_RECEIVER.getType()));
        if (normalReceiver) {
            list = list.stream().filter(el -> !Objects.equals(el.getAccountType(), AccountTypeEnum.PLATFORM.getType()) && !Objects.equals(el.getAccountType(), AccountTypeEnum.SUPPLIER.getType())).collect(Collectors.toList());
        }

        Map<Long, List<BillProfitSharingRule>> accountMap = list.stream().collect(Collectors.groupingBy(BillProfitSharingRule::getAccountId));
        accountMap.forEach((accountId, byAccountIdList) -> {
            ProfitSharingRuleVO vo = new ProfitSharingRuleVO();
            BillProfitSharingRule baseInfo = byAccountIdList.get(0);
            vo.setRoleCode(baseInfo.getRoleCode());
            vo.setProfitSharingMode(baseInfo.getProfitSharingMode());
            vo.setAccountId(baseInfo.getAccountId());
            TenantVO tenantVO = tenantMap.get(accountId);
            if (!ObjectUtils.isEmpty(tenantVO)) {
                vo.setName(ObjectUtils.isEmpty(tenantVO.getCompanyName()) ? tenantVO.getTenantName() : tenantVO.getCompanyName());
                List<TenantAuthConnection> tenantAuthConnections = tenantAuthConnectionDao.listByParam(TenantAuthConnQueryDTO.builder()
                        .tenantId(accountId)
                        .build());
                vo.setHuiFuId(CollectionUtils.isEmpty(tenantAuthConnections) ? "" : tenantAuthConnections.get(0).getHuifuId());
            } else {
                HuiFuAccount account = huiFuAccountDao.getByAccountIdAndType(accountId, AccountTypeEnum.NORMAL_RECEIVER.getType());
                vo.setName(account == null ? "" : account.getRegName());
                vo.setHuiFuId(account == null ? "" : account.getHuifuId());
            }
            byAccountIdList.forEach(e -> {
                DeliveryUndertakerTypeEnum deliverTypeEnum = DeliveryUndertakerTypeEnum.getByCode(e.getDeliveryType());
                ProfitSharingRuleTypeEnum typeEnum = ProfitSharingRuleTypeEnum.getByCode(e.getType());
                switch (deliverTypeEnum) {
                    case BRAND_DELIVERY:
                        switch (typeEnum) {
                            case PROPRIETARY_SKU:
                                vo.setMappingTypeSkuProprietary(ObjectUtils.isEmpty(e.getMappingType()) ? ProfitSharingRuleMappingTypeEnum.SELF_RATIO.getCode() : e.getMappingType());
                                vo.setNumberSkuProprietary(e.getNumber());
                                break;

                            case DELIVERY:
                                vo.setMappingTypeDeliveryProprietary(ObjectUtils.isEmpty(e.getMappingType()) ? ProfitSharingRuleMappingTypeEnum.SELF_RATIO.getCode() : e.getMappingType());
                                vo.setNumberDeliveryProprietary(e.getNumber());
                                break;

                            case SERVICE_CHARGE:
                                vo.setMappingTypeServicechargeProprietary(ObjectUtils.isEmpty(e.getMappingType()) ? ProfitSharingRuleMappingTypeEnum.SELF_RATIO.getCode() : e.getMappingType());
                                vo.setNumberServicechargeProprietary(e.getNumber());
                                break;
                            default:
                                break;
                        }
                        break;
                    case THIRD_DELIVERY:
                        switch (typeEnum) {
                            case SUPPLY_SKU:
                                vo.setMappingTypeDeliveryThree(ObjectUtils.isEmpty(e.getMappingType()) ? ProfitSharingRuleMappingTypeEnum.SELF_RATIO.getCode() : e.getMappingType());
                                vo.setNumberDeliveryThree(e.getNumber());
                                break;

                            case DELIVERY:
                                vo.setMappingTypeSkuThree(ObjectUtils.isEmpty(e.getMappingType()) ? ProfitSharingRuleMappingTypeEnum.SELF_RATIO.getCode() : e.getMappingType());
                                vo.setNumberSkuThree(e.getNumber());
                                break;

                            case SERVICE_CHARGE:
                                vo.setMappingTypeServicechargeThree(ObjectUtils.isEmpty(e.getMappingType()) ? ProfitSharingRuleMappingTypeEnum.SELF_RATIO.getCode() : e.getMappingType());
                                vo.setNumberServicechargeThree(e.getNumber());
                                break;
                            default:
                                break;
                        }
                        break;
                    default:
                        break;
                }
            });
            result.add(vo);
        });
        // 按roleCode升序排列
        Collections.sort(result, new Comparator<ProfitSharingRuleVO>() {
            @Override
            public int compare(ProfitSharingRuleVO o1, ProfitSharingRuleVO o2) {
                return o1.getRoleCode()-o2.getRoleCode();
            }
        });
        return result;
    }

    private List<ProfitSharingRuleVO> initEmptyRuleList(Long tenantId) {
        List<ProfitSharingRuleVO> result = new ArrayList<>();
        ProfitSharingRuleVO profitSharingRuleVo1 = new ProfitSharingRuleVO();
        profitSharingRuleVo1.setName("帆台");
        profitSharingRuleVo1.setAccountId(ftTenantId);
        profitSharingRuleVo1.setRoleCode(ProfitSharingRuleRoleCodeEnum.RECEIVE.getCode());
        profitSharingRuleVo1.setType(1);

        ProfitSharingRuleVO profitSharingRuleVo2 = new ProfitSharingRuleVO();
        profitSharingRuleVo2.setName("鲜沐");
        profitSharingRuleVo2.setAccountId(xmTenantId);
        profitSharingRuleVo2.setRoleCode(ProfitSharingRuleRoleCodeEnum.RECEIVE.getCode());
        profitSharingRuleVo2.setType(1);

        // 品牌方作为分账方
        ProfitSharingRuleVO profitSharingRuleVo3 = new ProfitSharingRuleVO();
        TenantVO tenantVO = tenantService.queryTenantById(tenantId, TenantTypeEnum.BRAND.getCode());
        profitSharingRuleVo3.setName(tenantVO.getCompanyName());
        profitSharingRuleVo3.setAccountId(tenantId);
        profitSharingRuleVo3.setRoleCode(ProfitSharingRuleRoleCodeEnum.PAY.getCode());
        profitSharingRuleVo3.setType(1);

        result.add(profitSharingRuleVo3);
        result.add(profitSharingRuleVo1);
        result.add(profitSharingRuleVo2);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initRule(List<ProfitSharingRuleDTO> profitSharingRuleDtos) {
        List<BillProfitSharingRule> result = new ArrayList<>();
        List<Long> tenantIds = profitSharingRuleDtos.stream().map(ProfitSharingRuleDTO::getTenantId).collect(Collectors.toList());
        AtomicReference<Long> tenantId = new AtomicReference();
        profitSharingRuleDtos.forEach(dto -> {
            Integer roleCode = dto.getRoleCode();
            tenantId.set(dto.getTenantId());
            Long accountId = dto.getAccountId();

            BillProfitSharingRule bps = buildBillProfitSharingRule(roleCode, tenantId.get(), accountId, DeliveryUndertakerTypeEnum.BRAND_DELIVERY.getCode());
            bps.setDeliveryType(DeliveryUndertakerTypeEnum.BRAND_DELIVERY.getCode());
            bps.setType(ProfitSharingRuleTypeEnum.PROPRIETARY_SKU.getCode());
            bps.setMappingType(Objects.isNull(dto.getMappingTypeSkuProprietary()) ? ProfitSharingRuleMappingTypeEnum.SELF_RATIO.getCode() : dto.getMappingTypeSkuProprietary());
            bps.setNumber(dto.getNumberSkuProprietary());
            result.add(bps);

            BillProfitSharingRule bd = buildBillProfitSharingRule(roleCode, tenantId.get(), accountId, DeliveryUndertakerTypeEnum.BRAND_DELIVERY.getCode());
            bd.setDeliveryType(DeliveryUndertakerTypeEnum.BRAND_DELIVERY.getCode());
            bd.setType(ProfitSharingRuleTypeEnum.DELIVERY.getCode());
            bd.setMappingType(Objects.isNull(dto.getMappingTypeDeliveryProprietary()) ? ProfitSharingRuleMappingTypeEnum.SELF_RATIO.getCode() : dto.getMappingTypeDeliveryProprietary());
            bd.setNumber(dto.getNumberDeliveryProprietary());
            result.add(bd);

            BillProfitSharingRule bsc = buildBillProfitSharingRule(roleCode, tenantId.get(), accountId, DeliveryUndertakerTypeEnum.BRAND_DELIVERY.getCode());
            bsc.setDeliveryType(DeliveryUndertakerTypeEnum.BRAND_DELIVERY.getCode());
            bsc.setType(ProfitSharingRuleTypeEnum.SERVICE_CHARGE.getCode());
            bsc.setMappingType(Objects.isNull(dto.getMappingTypeServicechargeProprietary()) ? ProfitSharingRuleMappingTypeEnum.SELF_RATIO.getCode() : dto.getMappingTypeServicechargeProprietary());
            bsc.setNumber(dto.getNumberServicechargeProprietary());
            result.add(bsc);
        });

        // 三方
        buildThirdRules(result, profitSharingRuleDtos);

        // 无仓规则初始化 品牌方和不确定的供应商
        List<BillProfitSharingRule> noWarehouseRules = buildNoWarehouseRules(tenantId.get());
        result.addAll(noWarehouseRules);

        chargeEnable(tenantId.get(), profitSharingRuleDtos);

        mapper.deleteByTenantId(tenantId.get());

        // 延迟、实时分账
        Integer profitSharingMode = profitSharingRuleDtos.get(0).getProfitSharingMode();
        result.forEach(profitSharingRule -> {profitSharingRule.setProfitSharingMode(profitSharingMode);});

        mapper.insertBatch(result);

        applicationEventPublisher.publishEvent(new TenantChangeEvent(tenantIds));
    }

    private void buildThirdRules(List<BillProfitSharingRule> result, List<ProfitSharingRuleDTO> profitSharingRuleDtos) {
        int counter = 0;
        for (ProfitSharingRuleDTO dto : profitSharingRuleDtos) {
            Integer roleCode = dto.getRoleCode();
            Long tenantId = dto.getTenantId();
            if (!tenantId.equals(dto.getAccountId())) {
                if (counter == 0) {
                    dto.setAccountId(ftTenantId);
                    counter++;
                } else {
                    dto.setAccountId(xmTenantId);
                }
            }
            Long accountId = dto.getAccountId();

            BillProfitSharingRule tsk = buildBillProfitSharingRule(roleCode, tenantId, accountId, DeliveryUndertakerTypeEnum.THIRD_DELIVERY.getCode());
            tsk.setDeliveryType(DeliveryUndertakerTypeEnum.THIRD_DELIVERY.getCode());
            tsk.setType(ProfitSharingRuleTypeEnum.SUPPLY_SKU.getCode());
            tsk.setMappingType(Objects.isNull(dto.getMappingTypeDeliveryThree()) ? ProfitSharingRuleMappingTypeEnum.PRICE.getCode() : dto.getMappingTypeDeliveryThree());
            tsk.setNumber(Objects.isNull(dto.getNumberDeliveryThree()) ? BigDecimal.ZERO : dto.getNumberDeliveryThree());
            result.add(tsk);

            BillProfitSharingRule td = buildBillProfitSharingRule(roleCode, tenantId, accountId, DeliveryUndertakerTypeEnum.THIRD_DELIVERY.getCode());
            td.setDeliveryType(DeliveryUndertakerTypeEnum.THIRD_DELIVERY.getCode());
            td.setType(ProfitSharingRuleTypeEnum.DELIVERY.getCode());
            td.setMappingType(Objects.isNull(dto.getMappingTypeSkuThree()) ? ProfitSharingRuleMappingTypeEnum.PRICE.getCode() : dto.getMappingTypeSkuThree());
            td.setNumber(Objects.isNull(dto.getNumberSkuThree()) ? BigDecimal.ZERO : dto.getNumberSkuThree());
            result.add(td);

            BillProfitSharingRule tsc = buildBillProfitSharingRule(roleCode, tenantId, accountId, DeliveryUndertakerTypeEnum.THIRD_DELIVERY.getCode());
            tsc.setDeliveryType(DeliveryUndertakerTypeEnum.THIRD_DELIVERY.getCode());
            tsc.setType(ProfitSharingRuleTypeEnum.SERVICE_CHARGE.getCode());
            tsc.setMappingType(Objects.isNull(dto.getMappingTypeServicechargeThree()) ? ProfitSharingRuleMappingTypeEnum.SELF_RATIO.getCode() : dto.getMappingTypeServicechargeThree());
            tsc.setNumber(dto.getNumberServicechargeThree());
            result.add(tsc);
        }

    }

    /**
     * 构建无仓规则
     * @param tenantId
     * @return
     */
    private List<BillProfitSharingRule> buildNoWarehouseRules(Long tenantId) {
        List<BillProfitSharingRule> result = new ArrayList<>();

        // Create tenant rules
        result.add(createRule(tenantId, tenantId, ProfitSharingRuleRoleCodeEnum.PAY.getCode(), ProfitSharingRuleTypeEnum.SUPPLY_SKU.getCode(), ProfitSharingRuleMappingTypeEnum.PRICE.getCode(), BigDecimal.ZERO));
        result.add(createRule(tenantId, tenantId, ProfitSharingRuleRoleCodeEnum.PAY.getCode(), ProfitSharingRuleTypeEnum.DELIVERY.getCode(), ProfitSharingRuleMappingTypeEnum.PRICE.getCode(), BigDecimal.ZERO));
        result.add(createRule(tenantId, tenantId, ProfitSharingRuleRoleCodeEnum.PAY.getCode(), ProfitSharingRuleTypeEnum.SERVICE_CHARGE.getCode(), ProfitSharingRuleMappingTypeEnum.AVERAGE_RATIO.getCode(), null));

        // Create supplier rules
        result.add(createRule(tenantId, null, ProfitSharingRuleRoleCodeEnum.RECEIVE.getCode(), ProfitSharingRuleTypeEnum.SUPPLY_SKU.getCode(), ProfitSharingRuleMappingTypeEnum.PRICE.getCode(), BigDecimal.ZERO));
        result.add(createRule(tenantId, null, ProfitSharingRuleRoleCodeEnum.RECEIVE.getCode(), ProfitSharingRuleTypeEnum.SERVICE_CHARGE.getCode(), ProfitSharingRuleMappingTypeEnum.AVERAGE_RATIO.getCode(), null));

        return result;
    }

    private BillProfitSharingRule createRule(Long tenantId, Long accountId, Integer roleCode, Integer type, Integer mappingType, BigDecimal number) {
        BillProfitSharingRule rule = new BillProfitSharingRule();
        rule.setTenantId(tenantId);
        rule.setDeliveryType(ProfitSharingDeliveryTypeEnums.NO_WAREHOUSE.getType());
        rule.setType(type);
        rule.setMappingType(mappingType);
        rule.setNumber(number);
        rule.setRoleCode(roleCode);
        rule.setAccountId(accountId);
        rule.setAccountType(accountId != null ? AccountTypeEnum.TENANT.getType() : AccountTypeEnum.SUPPLIER.getType());
        return rule;
    }

    @Override
    public void initNoWarehouseRule(List<Long> tenantIds) {
        if (CollectionUtils.isEmpty(tenantIds)) {
            tenantIds = mapper.selectAllTenantIds();
        }
        log.info("本次要初始化的租户有：{}", tenantIds);
        tenantIds.forEach(tenantId -> {
            List<BillProfitSharingRule> billProfitSharingRules = mapper.selectByTenantIdAndDeliveryTypes(tenantId, Lists.newArrayList(ProfitSharingDeliveryTypeEnums.NO_WAREHOUSE.getType()));
            if (!CollectionUtils.isEmpty(billProfitSharingRules)) {
                log.info("租户{}无仓规则已存在，无需初始化", tenantId);
                return;
            }
            List<BillProfitSharingRule> noWarehouseRules = buildNoWarehouseRules(tenantId);
            mapper.insertBatch(noWarehouseRules);
            log.info("租户{}无仓规则初始化完毕", tenantId);
        });
    }

    /**
     * 校验百分比
     *
     * @param tenantId
     * @param profitSharingRuleDtos
     */
    private void chargeEnable(Long tenantId, List<ProfitSharingRuleDTO> profitSharingRuleDtos) {
        ProductAgentSkuFeeRuleVO detail = productAgentSkuFeeRuleService.detail(tenantId);
        List<ProductAgentSkuFeeRuleDetailVO> details = detail.getDetails();
        // 如果是案件数，帆台的支付费率不能小于等于0
        if(ProductAgentSkuFeeRuleTypeEnum.ACCOUNT.getCode().equals(detail.getType())){
            Optional<ProfitSharingRuleDTO> first = profitSharingRuleDtos.stream().filter(e -> e.getAccountId().equals(ftTenantId)).findFirst();
            if(first.isPresent()){
                ProfitSharingRuleDTO profitSharingRuleDTO = first.get();
                BigDecimal numberServicechargeThree = getNumberServicechargeThree(profitSharingRuleDTO);
                if (numberServicechargeThree != null && numberServicechargeThree.compareTo(BigDecimal.ZERO) > 0) {
                    throw new ParamsException("请检查数据，手续费不可以大于入账金额");
                }
            }
            return;
        }

        Map<Integer, ProductAgentSkuFeeRuleDetailVO> skuFeeRuleMap = null;

        if (ProductAgentSkuFeeRuleTypeEnum.SELF_RATIO.getCode().equals(detail.getType())) {
            skuFeeRuleMap = details.stream().collect(Collectors.toMap(ProductAgentSkuFeeRuleDetailVO::getMemberCode, Function.identity()));
        }
        Map<Integer, ProductAgentSkuFeeRuleDetailVO> finalSkuFeeRuleMap = skuFeeRuleMap;


        profitSharingRuleDtos.forEach(e -> {

            Integer mappingTypeServicechargeProprietary = e.getMappingTypeServicechargeProprietary();
            if (ProfitSharingRuleMappingTypeEnum.SELF_RATIO.getCode().equals(mappingTypeServicechargeProprietary)) {
                BigDecimal numberServicechargeProprietary = e.getNumberServicechargeProprietary().multiply(BigDecimal.valueOf(0.002));
                if (e.getNumberSkuProprietary().compareTo(numberServicechargeProprietary) < 0 || e.getNumberDeliveryProprietary().compareTo(numberServicechargeProprietary) < 0) {
                    throw new ParamsException("请检查数据，手续费不可以大于入账金额");
                }
            }

            BigDecimal numberServicechargeThree = getNumberServicechargeThree(e);
            if (!CollectionUtils.isEmpty(finalSkuFeeRuleMap) && ObjectUtil.isNotNull(numberServicechargeThree) ) {
                if (e.getAccountId().equals(xmTenantId)) {
                    ProductAgentSkuFeeRuleDetailVO productAgentSkuFeeRuleDetailVO = finalSkuFeeRuleMap.get(TenantTypeEnum.SUPPLY.getCode());
                    BigDecimal percentage = productAgentSkuFeeRuleDetailVO.getPercentage();
                    if (percentage.compareTo(numberServicechargeThree) < 0) {
                        throw new ParamsException("请检查数据，手续费不可以大于入账金额");
                    }
                } else if (e.getAccountId().equals(ftTenantId)) {
                    ProductAgentSkuFeeRuleDetailVO productAgentSkuFeeRuleDetailVO = finalSkuFeeRuleMap.get(TenantTypeEnum.FANTAI.getCode());
                    BigDecimal percentage = productAgentSkuFeeRuleDetailVO.getPercentage();
                    if (percentage.compareTo(numberServicechargeThree) < 0) {
                        throw new ParamsException("请检查数据，手续费不可以大于入账金额");
                    }
                } else if (e.getAccountId().equals(tenantId)) {
                    ProductAgentSkuFeeRuleDetailVO productAgentSkuFeeRuleDetailVO = finalSkuFeeRuleMap.get(TenantTypeEnum.BRAND.getCode());
                    BigDecimal percentage = ObjectUtils.isEmpty(productAgentSkuFeeRuleDetailVO) ? BigDecimal.ZERO : productAgentSkuFeeRuleDetailVO.getPercentage();
                    if (percentage.compareTo(numberServicechargeThree) < 0) {
                        throw new ParamsException("请检查数据，手续费不可以大于入账金额");
                    }
                }
            }
            if (Objects.isNull(e.getProfitSharingMode())) {
                throw new ParamsException("请检查数据，分账模式不能为空");
            }
        });
    }
    private BigDecimal getNumberServicechargeThree(ProfitSharingRuleDTO e){
        Integer mappingTypeServicechargeThree = e.getMappingTypeServicechargeThree();
        if (ProfitSharingRuleMappingTypeEnum.SELF_RATIO.getCode().equals(mappingTypeServicechargeThree)) {
            return e.getNumberServicechargeThree().multiply(BigDecimal.valueOf(0.002));
        }
        return null;
    }

    private BillProfitSharingRule buildBillProfitSharingRule(Integer roleCode, Long tenantId, Long accountId, Integer deliveryType) {
        BillProfitSharingRule billProfitSharingRule = new BillProfitSharingRule();
        billProfitSharingRule.setTenantId(tenantId);
        billProfitSharingRule.setAccountId(accountId);
        billProfitSharingRule.setRoleCode(roleCode);
        if (Objects.equals(deliveryType, DeliveryUndertakerTypeEnum.BRAND_DELIVERY.getCode())) {
            if (Objects.equals(tenantId, accountId)) {
                billProfitSharingRule.setAccountType(AccountTypeEnum.TENANT.getType());
            } else {
                billProfitSharingRule.setAccountType(AccountTypeEnum.NORMAL_RECEIVER.getType());
            }
        }
        if (Objects.equals(deliveryType, DeliveryUndertakerTypeEnum.THIRD_DELIVERY.getCode())) {
            billProfitSharingRule.setAccountType(AccountTypeEnum.getByAccountId(tenantId, accountId));
        }
        return billProfitSharingRule;
    }
}
