package com.cosfo.oms.common.constant;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/24 12:50
 */
public class Constants {

    /**
     * icon key
     */
    public static final String ICON_KEY = "init_classification_icon";

    /**
     * excel目录
     */
    public static final String EXCEL_DIRECTORY = "excel";

    /**
     * 斜杠常量
     */
    public static final String SLASH = "/";


    /**
     * 时间格式
     */
    public static final String TIME_FORMAT_SHORT = "yyyy-MM-dd";

    /**
     * 完整时间格式
     */
    public static final String TIME_FORMAT_LONG = "yyyy-MM-dd HH:mm:ss";

    /**
     * 时间后缀常量
     */
    public static final String END_TIME = " 23:59:59";

    /**
     * one hundred
     */
    public static final BigDecimal ONE_HUNDRED = BigDecimal.valueOf(100);

    /**
     * 1000
     */
    public static final BigDecimal ONE_THOUSAND = BigDecimal.valueOf(1000);

    /**
     * 0
     */
    public static final BigDecimal ZERO = BigDecimal.valueOf(0);

    /**
     * 10万
     */
    public static final BigDecimal ONE_HUNDRED_THOUSAND = BigDecimal.valueOf(100000);

    public static final String DIM_CLASS_DRIVER_LICENSE_TYPE = "driverLicenseType";

    /**
     * 应用标识
     */
    public static final String COSFO_PREFIX = "cosfo_";
    /**
     * 一级分类默认父类id
     */
    public static final Long DEFAULT_CLASSIFICATION_ID = 0L;
    /**
     * 类目缓存
     */
    public static final String CATEGORY_CACHE = "category_cache";
    /**
     * 星号 asterisk
     */
    public static final String ASTERISK = "*";
    /**
     * 文件七牛云路径
     */
    public static final String FILE_DIR = "file/";
    /**
     * 订单编号
     */
    public static final String ORDER_NO = "订单编号";
    /**
     * 售后id
     */
    public static final String ORDER_AFTER_SALE_ID = "orderAfterSaleId";
    /**
     * time
     */
    public static final String TIME = "time";

    /**
     * "application/json"
     */
    public static final String JSON_CONTENT_TYPE = "application/json";

    /**
     * appID
     */
    public static final String APPID = "tp_app_id";

    /**
     * 汇付日期格式
     */
    public static final String HUIFU_DATE = "yyyyMMdd";

    /**
     * 汇付接收空数组格式
     */
    public static final String HUIFU_EMPTY_LIST = "[{}]";

    /**
     * 时间
     */
    public static final String QUERY_TIME = "时间";

    /**
     * 预付交易流水预付记录id前缀
     */
    public static final String PREPAYMENT_TRANSACTION_ID_PREFIX = "PT";

    /**
     * 订单状态
     */
    public static final String STATUS = "订单状态";

    /**
     * 配送仓类型
     */
    public static final String WAREHOUSE_TYPE = "配送仓类型";

    /**
     * 支付方式
     */
    public static final String PAY_TYPE = "支付方式";

    /**
     * 订单号
     */
    public static final String ORDER_NO_CN = "订单号";

    /**
     * 起始时间
     */
    public static final String ORDER_START_TIME = "起始时间";

    /**
     * 手机号
     */
    public static final String PHONE = "注册手机号";

    /**
     * 截止时间
     */
    public static final String ORDER_END_TIME = "截止时间";

    /**
     * 外部订单号
     */
    public static final String CUSTOMER_ORDER_ID = "外部订单号";


    /**
     * 商品名称
     */
    public static final String TITLE = "商品名称";

    /**
     * 类目
     */
    public static final String CATEGORY = "类目";

    /**
     * 商品编码
     */
    public static final String ITEM_ID = "商品编码";

    /**
     * 门店名称
     */
    public static final String STORE_NAME = "门店名称";

    /**
     * 商城名称
     */
    public static final String TENANT_NAME = "商城名称";

    /**
     * 门店类型
     */
    public static final String STORE_TYPE = "门店类型";

    /**
     * 全部
     */
    public static final String TOTAL = "全部";


    /**
     * 售后类型
     */
    public static final String AFTER_SALE_TYPE = "售后类型";

    /**
     * 售后服务类型
     */
    public static final String SERVICE_TYPE = "售后服务类型";

    /**
     * 退款方式
     */
    public static final String AFTER_SALE_PAY_TYPE = "退款方式";

    /**
     * 售后订单编号
     */
    public static final String AFTER_SALE_ORDER_NO = "售后订单编号";

    /**
     * 售后订单状态
     */
    public static final String ORDER_AFTER_SALE_STATUS = "售后订单状态";


    /**
     * 网易七鱼插件appid
     */
    public static final String QIYU_APPID = "wxae5e29812005203f";

    public static final String QIYU_VERSION = "1.4.17";

    /**
     * H5请求路径
     */
    public static final String COSFO_MALL_H5_URL = "?";

    /**
     * 售后审核时间
     */
    public static final String HANDLE_TIME = "审核时间";

    /**
     * 售后操作人
     */
    public static final String OPERATOR_NAME = "操作人";

    /**
     * 售后凭证照片
     */
    public static final String PROOF_PICTURE = "最后凭证照片";

    /**
     * 门店状态
     */
    public static final String STORE_STATUS = "门店状态";

    public static final String STORE_ID = "门店ID";
    public static final String STORE_NO = "门店编号";
    public static final String CONTACT_PHONE = "联系人手机哈";
    public static final String AUDIT_STATUS = "审核状态";
    public static final String EXPORT_START_TIME = "开始时间";
    public static final String EXPORT_END_TIME = "结束时间";
    /**
     * 4 代仓-经销，5-pop 不进入saas
     */
    public static final List<Integer> NOT_IN_SUB_AGENT_TYPE_LIST = Arrays.asList (4, 5);
    public static final String TENANT_PRIVILEGES = "cosfo_tenant_privileges_";


    /**
     * 密码校验正则:至少包含一个大写、小写、数值的8~20位字符串,支持特殊字符
     */
    public static final String PASSWORD_REGEX = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d-!@#$%^&*()_+{}|\\\\:;\"'<>,.?/~`+=]{8,20}$";

    public static final String POI_AUDIT_MESSAGE = "%s POI更改 %s，与系统相差%s";

    public static final String POI_AUDIT_NOTIFY = "**SaaS门店地址变更申请**\n" +
            "申请人：配送端司机\n" +
            "审批事理：%s\n" +
            "[点击查看地址变更审批详情](%s)";
}
