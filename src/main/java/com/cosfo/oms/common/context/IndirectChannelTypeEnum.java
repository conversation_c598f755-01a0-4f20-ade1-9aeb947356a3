package com.cosfo.oms.common.context;

import lombok.Getter;
import net.summerfarm.payment.routing.common.enums.PaymentDictionaryEnums;

/**
 * @description: 间连渠道类型枚举
 * @author: Gemini
 * @date: 2025-08-18
 */
@Getter
public enum IndirectChannelTypeEnum {
    HUI_FU(1, PaymentDictionaryEnums.ChannelName.HUI_FU.getName()),
    DIN(2, PaymentDictionaryEnums.ChannelName.DIN_PAY.getName());

    private final Integer code;
    private final String description;

    IndirectChannelTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public static String getDesc(Integer code) {
        for (IndirectChannelTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type.getDescription();
            }
        }
        return null;
    }
}
