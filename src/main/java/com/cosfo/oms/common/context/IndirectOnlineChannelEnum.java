package com.cosfo.oms.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2025-08-18
 **/
@Getter
@AllArgsConstructor
public enum IndirectOnlineChannelEnum {

    /**
     * 汇付
     */
    HUI_FU(1, "汇付"),
    /**
     * 智付
     */
    ZHI_FU(2, "智付");

    /**
     * 类型
     */
    private Integer type;

    /**
     * 描述
     */
    private String desc;
}
