package com.cosfo.oms.common.interceptor;

import com.alibaba.fastjson.JSON;
import java.lang.reflect.Method;
import javax.servlet.http.HttpServletRequest;

import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.CommonResult;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

@Aspect
@Component
@Slf4j
public class LogControllerInputOutputAspect {

  @Around("@within(org.springframework.web.bind.annotation.RestController) && execution(* *.*(..))")
  public Object logController(ProceedingJoinPoint joinPoint) throws Throwable {
    return doLog(joinPoint);
  }

  public Object doLog(ProceedingJoinPoint joinPoint) throws Throwable {
    MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
    Method method = methodSignature.getMethod();
    String[] parameterNames = methodSignature.getParameterNames();
    Object[] args = joinPoint.getArgs();

    log.info("Requested method: {}.{}()\nParameters:{}, arg values:{}",
        joinPoint.getTarget().getClass().getSimpleName(),
        method.getName(), JSON.toJSONString(parameterNames, SerializerFeature.IgnoreErrorGetter),
        JSON.toJSONString(args, SerializerFeature.IgnoreErrorGetter));

    long time = System.currentTimeMillis();
    Object result = joinPoint.proceed();
    long costTime = System.currentTimeMillis() - time;

    if (result != null && result instanceof CommonResult) {
      log.info("Method:{}() response: {}, RT:{}ms", method.getName(), JSON.toJSONString(result),
          costTime);
    } else {
      log.info("Method:{}() response: null, RT:{}ms", method.getName(), costTime);
    }

    return result;
  }
}
