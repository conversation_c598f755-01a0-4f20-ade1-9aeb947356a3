package com.cosfo.oms.facade;

import com.cosfo.oms.common.context.IndirectChannelTypeEnum;
import com.cosfo.oms.common.context.PaymentMethodSwitchEnum;
import com.cosfo.oms.common.context.PaymentSceneEnum;
import com.cosfo.oms.model.dto.LoginContextInfoDTO;
import com.cosfo.oms.tenant.model.dto.TenantAuthServiceDTO;
import net.summerfarm.client.provider.payment.PaymentChannelProvider;
import net.summerfarm.client.req.payment.PaymentChannelSaveReq;
import net.summerfarm.client.req.payment.PaymentSceneConfigReq;
import net.summerfarm.client.resp.payment.PaymentChannelSaveResp;
import net.summerfarm.payment.routing.common.enums.PaymentBusinessLineEnums;
import net.xianmu.common.exception.BizException;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @description: 支付渠道门面
 * @author: Gemini
 * @date: 2025-08-18
 */
@Component
public class PaymentChannelFacade {

    @Reference
    private PaymentChannelProvider paymentChannelProvider;

    /**
     * 保存支付渠道配置到中央渠道路由服务
     *
     * @param authDto  包含渠道配置信息的DTO
     * @return 从中央服务返回的渠道配置ID
     */
    public Long saveChannelConfig(TenantAuthServiceDTO authDto) {
        PaymentChannelSaveReq req = new PaymentChannelSaveReq();

        // 使用枚举填充通用参数
        req.setTenantId(authDto.getTenantId());
        req.setBusinessLine(PaymentBusinessLineEnums.SAAS.getCode());

        Integer channelType = authDto.getIndirectOnlineChannel();
        req.setChannelName(IndirectChannelTypeEnum.getDesc(channelType));

        // 参数映射
        if (IndirectChannelTypeEnum.HUI_FU.getCode().equals(channelType)) {
            req.setMerchantNo(authDto.getHuifuId());
            req.setPrivateKey(authDto.getSecretKey());
            req.setPublicKey(authDto.getHuifuPublicKey());
        } else if (IndirectChannelTypeEnum.DIN.getCode().equals(channelType)) {
            req.setMerchantNo(authDto.getDinMerchantNo());
            req.setPrivateKey(authDto.getDinPrivateKey());
            req.setPublicKey(authDto.getDinPublicKey());
            req.setSecret(authDto.getDinSecret());
        } else if (Objects.equals(authDto.getWechatDirectSwitch(), PaymentMethodSwitchEnum.ENABLED.getCode())) {
            req.setMerchantNo(authDto.getPayMchid());
            req.setSecret(authDto.getPaySecret());
            req.setCertPath(authDto.getPayCertPath());
        }

        // 构建并设置支付场景
        req.setSceneConfigs(buildSceneConfigs(authDto));

        PaymentChannelSaveResp resp = paymentChannelProvider.saveChannel(req);

        if (resp == null || resp.getChannelId() == null) {
            throw new BizException("创建支付渠道配置失败，中央服务未返回渠道ID");
        }
        return resp.getChannelId();
    }

    /**
     * 根据 DTO 中的开关状态，构建支付场景列表
     */
    private List<PaymentSceneConfigReq> buildSceneConfigs(TenantAuthServiceDTO authDto) {
        List<PaymentSceneConfigReq> sceneConfigs = new ArrayList<>();

        // 检查小程序微信支付开关
        if (PaymentMethodSwitchEnum.ENABLED.getCode().equals(authDto.getAppletWechatPaySwitch())) {
            sceneConfigs.add(createSceneReq(PaymentSceneEnum.APPLET_WECHAT_PAY));
        }
        // 检查H5微信支付开关
        if (PaymentMethodSwitchEnum.ENABLED.getCode().equals(authDto.getH5WechatIndirectSwitch())) {
            sceneConfigs.add(createSceneReq(PaymentSceneEnum.H5_WECHAT_PAY));
        }
        // 检查支付宝支付开关
        if (PaymentMethodSwitchEnum.ENABLED.getCode().equals(authDto.getAliIndirectSwitch())) {
            sceneConfigs.add(createSceneReq(PaymentSceneEnum.H5_ALI_PAY));
        }

        return sceneConfigs;
    }

    /**
     * 工具方法：根据枚举创建场景请求对象
     */
    private PaymentSceneConfigReq createSceneReq(PaymentSceneEnum scene) {
        PaymentSceneConfigReq sceneReq = new PaymentSceneConfigReq();
        sceneReq.setSceneName(scene.getSceneName());
        sceneReq.setPlatform(scene.getPlatform());
        sceneReq.setPaymentMethod(scene.getPaymentMethod());
        return sceneReq;
    }
}
