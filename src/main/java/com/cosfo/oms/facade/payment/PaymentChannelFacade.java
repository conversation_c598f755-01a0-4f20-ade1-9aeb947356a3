package com.cosfo.oms.facade.payment;

import cn.hutool.core.lang.Opt;
import com.cosfo.oms.common.context.IndirectChannelTypeEnum;
import com.cosfo.oms.common.context.PaymentMethodSwitchEnum;
import com.cosfo.oms.common.context.PaymentSceneEnum;
import com.cosfo.oms.tenant.model.dto.TenantAuthServiceDTO;
import com.cosfo.oms.tenant.service.TenantInfoService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.client.provider.payment.PaymentChannelProvider;
import net.summerfarm.client.req.payment.PaymentChannelSaveReq;
import net.summerfarm.client.req.payment.PaymentSceneConfigReq;
import net.summerfarm.client.resp.payment.PaymentChannelSaveResp;
import net.summerfarm.payment.routing.common.enums.PaymentBusinessLineEnums;
import net.summerfarm.payment.routing.common.enums.PaymentDictionaryEnums;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.tenant.resp.TenantAndBusinessInfoResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @description: 支付渠道门面
 * @author: Gemini
 * @date: 2025-08-18
 */
@Component
@Slf4j
public class PaymentChannelFacade {

    @DubboReference
    private PaymentChannelProvider paymentChannelProvider;
    @Resource
    private TenantInfoService tenantInfoService;

    /**
     * 渠道配置数据结构
     */
    @Data
    public static class ChannelConfig {
        private Long tenantId;
        private String channelName;
        private String merchantNo;
        private String privateKey;
        private String publicKey;
        private String secret;
        private String certPath;
        private List<PaymentSceneConfigReq> sceneConfigs;

        public ChannelConfig(Long tenantId, String channelName) {
            this.tenantId = tenantId;
            this.channelName = channelName;
            this.sceneConfigs = new ArrayList<>();
        }
    }

    /**
     * 保存多个支付渠道配置到中央渠道路由服务
     *
     * @param authDto  包含渠道配置信息的DTO
     * @return 保存成功的渠道配置ID列表
     */
    public List<Long> saveMultiChannelConfigs(TenantAuthServiceDTO authDto) {
        log.info("开始保存多渠道配置，租户ID: {}", authDto.getTenantId());

        // 1. 识别启用的渠道
        List<ChannelConfig> enabledChannels = identifyEnabledChannels(authDto);

        if (CollectionUtils.isEmpty(enabledChannels)) {
            log.warn("租户 {} 没有启用任何支付渠道", authDto.getTenantId());
            return new ArrayList<>();
        }

        log.info("租户 {} 启用了 {} 个支付渠道", authDto.getTenantId(), enabledChannels.size());

        // 2. 构建批量请求
        List<PaymentChannelSaveReq> batchRequests = buildBatchRequests(enabledChannels, authDto.getTenantId());

        // 3. 批量调用中央服务
        return saveBatchChannelConfigs(batchRequests, authDto.getTenantId());
    }

    /**
     * 构建批量请求
     */
    private List<PaymentChannelSaveReq> buildBatchRequests(List<ChannelConfig> enabledChannels, Long tenantId) {
        // 获取租户信息（批量获取，避免重复查询）
        List<TenantAndBusinessInfoResultResp> tenantAndCompanyInfoList = tenantInfoService.getTenantAndCompanyInfoList(Collections.singletonList(tenantId));
        if (CollectionUtils.isEmpty(tenantAndCompanyInfoList)) {
            throw new BizException("租户信息不存在");
        }
        TenantAndBusinessInfoResultResp tenantAndCompanyInfo = tenantAndCompanyInfoList.get(0);
        String companyEntity = Opt.ofNullable(tenantAndCompanyInfo.getCompanyName()).orElse(tenantAndCompanyInfo.getTenantName());

        List<PaymentChannelSaveReq> requests = new ArrayList<>();

        for (ChannelConfig channelConfig : enabledChannels) {
            PaymentChannelSaveReq req = new PaymentChannelSaveReq();

            // 填充通用参数
            req.setTenantId(channelConfig.getTenantId());
            req.setBusinessLine(PaymentBusinessLineEnums.SAAS.getCode());
            req.setChannelName(channelConfig.getChannelName());
            req.setCompanyEntity(companyEntity);

            // 填充渠道特定参数
            req.setMerchantNo(channelConfig.getMerchantNo());
            req.setPrivateKey(channelConfig.getPrivateKey());
            req.setPublicKey(channelConfig.getPublicKey());
            req.setSecret(channelConfig.getSecret());
            req.setCertPath(channelConfig.getCertPath());

            // 设置支付场景
            req.setSceneConfigs(channelConfig.getSceneConfigs());

            requests.add(req);
        }

        return requests;
    }

    /**
     * 批量保存渠道配置
     */
    private List<Long> saveBatchChannelConfigs(List<PaymentChannelSaveReq> requests, Long tenantId) {
        log.info("批量调用中央服务保存渠道配置，租户ID: {}, 渠道数量: {}", tenantId, requests.size());

        try {
            // 调用批量接口
            DubboResponse<List<PaymentChannelSaveResp>> response = paymentChannelProvider.saveChannel(requests);

            if (!response.isSuccess() || response.getData() == null) {
                log.error("批量保存渠道配置失败，租户ID: {}, 错误信息: {}", tenantId, response.getMsg());
                throw new BizException("批量保存渠道配置失败: " + response.getMsg());
            }

            List<PaymentChannelSaveResp> respList = response.getData();
            List<Long> channelIds = new ArrayList<>();

            // 处理响应结果
            for (int i = 0; i < respList.size(); i++) {
                PaymentChannelSaveResp resp = respList.get(i);
                PaymentChannelSaveReq req = requests.get(i);

                if (resp == null || resp.getChannelId() == null) {
                    log.error("渠道配置保存失败，租户ID: {}, 渠道: {}, 响应: {}",
                        tenantId, req.getChannelName(), resp);
                    throw new BizException("渠道配置保存失败: " + req.getChannelName());
                }

                channelIds.add(resp.getChannelId());
                log.info("成功保存渠道配置，租户ID: {}, 渠道: {}, 渠道ID: {}",
                    tenantId, req.getChannelName(), resp.getChannelId());
            }

            log.info("批量保存渠道配置完成，租户ID: {}, 成功保存 {} 个渠道", tenantId, channelIds.size());
            return channelIds;

        } catch (Exception e) {
            log.error("批量保存渠道配置异常，租户ID: {}", tenantId, e);
            throw new BizException("批量保存渠道配置异常: " + e.getMessage());
        }
    }

    /**
     * 工具方法：根据枚举创建场景请求对象
     */
    private PaymentSceneConfigReq createSceneReq(PaymentSceneEnum scene) {
        PaymentSceneConfigReq sceneReq = new PaymentSceneConfigReq();
        sceneReq.setSceneName(scene.getSceneName());
        sceneReq.setPlatform(scene.getPlatform());
        sceneReq.setPaymentMethod(scene.getPaymentMethod());
        return sceneReq;
    }

    /**
     * 识别启用的支付渠道
     *
     * @param authDto 租户认证服务DTO
     * @return 启用的渠道配置列表
     */
    private List<ChannelConfig> identifyEnabledChannels(TenantAuthServiceDTO authDto) {
        List<ChannelConfig> enabledChannels = new ArrayList<>();

        // 1. 微信直连渠道开始 目前只作用于小程序
        // 小程序开关开启，微信直连渠道开启
        if (Objects.equals(authDto.getAppletWechatPaySwitch(), PaymentMethodSwitchEnum.ENABLED.getCode()) && PaymentMethodSwitchEnum.ENABLED.getCode().equals(authDto.getWechatDirectSwitch())) {
            ChannelConfig wechatDirectConfig = buildWechatDirectConfig(authDto);
            if (wechatDirectConfig != null) {
                enabledChannels.add(wechatDirectConfig);
                log.info("识别到微信直连渠道，租户ID: {}", authDto.getTenantId());
            }
        }

        // 2. 检查H5间连
        boolean hasIndirectChannel = PaymentMethodSwitchEnum.ENABLED.getCode().equals(authDto.getWechatIndirectSwitch())
                || PaymentMethodSwitchEnum.ENABLED.getCode().equals(authDto.getWechatIndirectPluginSwitch())
                || PaymentMethodSwitchEnum.ENABLED.getCode().equals(authDto.getH5WechatIndirectSwitch())
                || PaymentMethodSwitchEnum.ENABLED.getCode().equals(authDto.getAliIndirectSwitch());

        if (hasIndirectChannel && authDto.getIndirectOnlineChannel() != null) {
            ChannelConfig indirectConfig = buildIndirectChannelConfig(authDto);
            if (indirectConfig != null) {
                enabledChannels.add(indirectConfig);
                log.info("识别到间连渠道，租户ID: {}, 渠道类型: {}",
                    authDto.getTenantId(), IndirectChannelTypeEnum.getDesc(authDto.getIndirectOnlineChannel()));
            }
        }

        return enabledChannels;
    }

    /**
     * 构建微信直连渠道配置
     */
    private ChannelConfig buildWechatDirectConfig(TenantAuthServiceDTO authDto) {
        if (StringUtils.isEmpty(authDto.getPayMchid()) ||
            StringUtils.isEmpty(authDto.getPaySecret()) ||
            StringUtils.isEmpty(authDto.getPayCertPath())) {
            log.warn("微信直连配置参数不完整，租户ID: {}", authDto.getTenantId());
            return null;
        }

        ChannelConfig config = new ChannelConfig(authDto.getTenantId(), PaymentDictionaryEnums.ChannelName.WECHAT.getName());
        config.setMerchantNo(authDto.getPayMchid());
        config.setSecret(authDto.getPaySecret());
        config.setCertPath(authDto.getPayCertPath());

        // 添加支付场景
        List<PaymentSceneConfigReq> sceneConfigs = new ArrayList<>();
        if (PaymentMethodSwitchEnum.ENABLED.getCode().equals(authDto.getAppletWechatPaySwitch()) &&
            PaymentMethodSwitchEnum.ENABLED.getCode().equals(authDto.getWechatDirectSwitch())) {
            sceneConfigs.add(createSceneReq(PaymentSceneEnum.APPLET_WECHAT_PAY));
        }
        config.setSceneConfigs(sceneConfigs);

        return config;
    }

    /**
     * 构建间连渠道配置
     */
    private ChannelConfig buildIndirectChannelConfig(TenantAuthServiceDTO authDto) {
        Integer channelType = authDto.getIndirectOnlineChannel();
        String channelName = IndirectChannelTypeEnum.getDesc(channelType);

        if (StringUtils.isEmpty(channelName)) {
            log.warn("未知的间连渠道类型: {}, 租户ID: {}", channelType, authDto.getTenantId());
            return null;
        }

        ChannelConfig config = new ChannelConfig(authDto.getTenantId(), channelName);

        // 根据渠道类型设置参数
        if (IndirectChannelTypeEnum.HUI_FU.getCode().equals(channelType)) {
            if (StringUtils.isEmpty(authDto.getHuifuId()) ||
                StringUtils.isEmpty(authDto.getSecretKey()) ||
                StringUtils.isEmpty(authDto.getHuifuPublicKey())) {
                log.warn("汇付渠道配置参数不完整，租户ID: {}", authDto.getTenantId());
                return null;
            }
            config.setMerchantNo(authDto.getHuifuId());
            config.setPrivateKey(authDto.getSecretKey());
            config.setPublicKey(authDto.getHuifuPublicKey());
        } else if (IndirectChannelTypeEnum.DIN.getCode().equals(channelType)) {
            if (StringUtils.isEmpty(authDto.getDinMerchantNo()) ||
                StringUtils.isEmpty(authDto.getDinPrivateKey()) ||
                StringUtils.isEmpty(authDto.getDinPublicKey()) ||
                StringUtils.isEmpty(authDto.getDinSecret())) {
                log.warn("智付渠道配置参数不完整，租户ID: {}", authDto.getTenantId());
                return null;
            }
            config.setMerchantNo(authDto.getDinMerchantNo());
            config.setPrivateKey(authDto.getDinPrivateKey());
            config.setPublicKey(authDto.getDinPublicKey());
            config.setSecret(authDto.getDinSecret());
        }

        // 添加支付场景
        List<PaymentSceneConfigReq> sceneConfigs = new ArrayList<>();
        if ((PaymentMethodSwitchEnum.ENABLED.getCode().equals(authDto.getWechatIndirectSwitch()) ||
                PaymentMethodSwitchEnum.ENABLED.getCode().equals(authDto.getWechatIndirectPluginSwitch())) &&
            PaymentMethodSwitchEnum.ENABLED.getCode().equals(authDto.getAppletWechatPaySwitch())) {
            sceneConfigs.add(createSceneReq(PaymentSceneEnum.APPLET_WECHAT_PAY));
        }
        if (PaymentMethodSwitchEnum.ENABLED.getCode().equals(authDto.getH5WechatIndirectSwitch())) {
            sceneConfigs.add(createSceneReq(PaymentSceneEnum.H5_WECHAT_PAY));
        }
        if (PaymentMethodSwitchEnum.ENABLED.getCode().equals(authDto.getAliIndirectSwitch())) {
            sceneConfigs.add(createSceneReq(PaymentSceneEnum.H5_ALI_PAY));
        }
        config.setSceneConfigs(sceneConfigs);

        return config;
    }
}
