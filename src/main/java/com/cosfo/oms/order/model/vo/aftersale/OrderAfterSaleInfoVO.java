package com.cosfo.oms.order.model.vo.aftersale;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
public class OrderAfterSaleInfoVO {

    /**
     * 订单编号
     */
    private Long orderId;

    /**
     * 订单项Id
     */
    private Long orderItemId;

    /**
     * 售后订单编号
     */
    private String afterSaleOrderNo;
    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 门店Id
     */
    private Long storeId;
    /**
     * 售后类型
     */
    private Integer afterSaleType;
    /**
     * 商品名称
     */
    private String title;
    /**
     * 售后服务类型
     */
    private Integer serviceType;

    /**
     * 退款方式（取支付方式） 1、微信支付 2、账期 3、余额支付 4、支付宝支付 5、无需支付 6、线下支付 7、非现金支付 8、组合支付
     */
    private Integer payType;

    /**
     * 售后金额
     */
    private BigDecimal totalPrice;
    /**
     * 申请人
     */
    private String accountName;
    /**
     * 注册手机号
     */
    private String phone;
    /**
     * 状态 1待审核 2处理中 3退款中 4已同意 5已拒绝 6已取消 7库存退还失败 8 待退款 9 三方处理中
     */
    private Integer status;
    /**
     * 下单日期
     */
    private LocalDateTime applyTime;

    /**
     * 售后类型描述
     */
    private String afterSaleTypeDesc;

    /**
     * 售后服务类型描述
     */
    private String serviceTypeDesc;
    /**
     * 退款方式描述
     */
    private String payTypeDesc;

    /**
     * 状态 1,待审核 2，已成功 3，已失败 4 ，已取消描述
     */
    private String statusDesc;

    /**
     * 申请金额
     */
    private BigDecimal applyPrice;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 实际退款金额
     */
    private BigDecimal returnPrice;

    /**
     * 仓库类型
     */
    private Integer warehouseType;

    /**
     * 商品id
     */
    private Long itemId;

    /**
     * 货品id
     */
    private Long skuId;

    /**
     * 自有编码
     */
    private String itemCode;
    /**
     * 售后原因
     */
    private String reason;

    /**
     * 最大售后数量
     */
    private Integer maxAfterSaleAmount;

    /**
     * 售后单位
     */
    private String afterSaleUnit;

    /**
     * 数量
     */
    private Integer amount;

    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 规格
     */
    private String specification;

    /**
     * 门店类型
     */
    private Integer storeType;

    /**
     * 门店类型描述
     */
    private String storeTypeDesc;

    /**
     * 门店分组name
     */
    private String merchantStoreGroupName;

    /**
     * 地址信息
     */
    private String address;

    /**
     * 供应商名称
     */
    private String supplierName;


    /**
     * 供应商id
     */
    private Long supplierTenantId;
    /**
     * 组合订单id
     */
    private Long combineOrderId;

    /**
     * 0-普通订单,1=组合订单
     */
    private Integer orderType;

    /**
     * 账号id
     */
    private Long accountId;

    /**
     * 售后单id
     */
    private Long id;

    /**
     * 货源desc
     */
    private String goodsTypeDesc;

    /**
     * 货源
     */
    private Integer goodsType;

    /**
     * 供应价
     */
    private BigDecimal supplyPrice;


    /**
     * 单价
     */
    private BigDecimal payablePrice;

    /**
     * 供应商申请退款金额
     */
    private BigDecimal supplyApplyRefundPrice;

    /**
     * 供应商实退金额
     */
    private BigDecimal supplyTotalRefundPrice;

    /**
     * 原订单编号
     */
    private String orderNo;

    /**
     * 操作人
     */
    private String operatorName;

    /**
     * 审核时间
     */
    private LocalDateTime handleTime;

    /**
     * 售后凭证照片
     */
    private String proofPicture;

    /**
     * 审核备注
     */
    private String handleRemark;

    /**
     *  退款凭证
     */
    private BigDecimal refundReceipt;
}
