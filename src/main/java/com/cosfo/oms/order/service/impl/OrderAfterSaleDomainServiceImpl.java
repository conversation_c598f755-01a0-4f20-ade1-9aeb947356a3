package com.cosfo.oms.order.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cofso.item.client.enums.GoodsTypeEnum;
import com.cofso.item.client.resp.MarketItemInfoResp;
import com.cosfo.common.excel.easyexcel.converter.EasyExcelLocalDateConverter;
import com.cosfo.common.excel.easyexcel.converter.LocalDateTimeConverter;
import com.cosfo.manage.client.order.aftersale.OrderAfterSaleProvider;
import com.cosfo.manage.client.order.req.OrderAfterSaleSelfReviewAgentReq;
import com.cosfo.oms.bill.mapper.BillProfitSharingOrderMapper;
import com.cosfo.oms.bill.model.po.BillProfitSharingOrder;
import com.cosfo.oms.bill.service.BillProfitSharingOrderService;
import com.cosfo.oms.common.constant.Constants;
import com.cosfo.oms.common.constant.NumberConstants;
import com.cosfo.oms.common.constant.SupplierTenantConstant;
import com.cosfo.oms.common.constant.XianmuSupplyTenant;
import com.cosfo.oms.common.context.*;
import com.cosfo.oms.common.context.OrderStatusEnum;
import com.cosfo.oms.common.context.WarehouseTypeEnum;
import com.cosfo.oms.common.convert.OrderItemMapper;
import com.cosfo.oms.common.convert.OrderMapper;
import com.cosfo.oms.common.exception.DefaultServiceException;
import com.cosfo.oms.common.executor.ExecutorFactory;
import com.cosfo.oms.common.result.ResultDTOEnum;
import com.cosfo.oms.common.utils.*;
import com.cosfo.oms.facade.CategoryServiceFacade;
import com.cosfo.oms.facade.MarketFacade;
import com.cosfo.oms.facade.OrderQueryFacade;
import com.cosfo.oms.facade.ProductQueryFacade;
import com.cosfo.oms.facade.aftersale.OrderAfterSaleCommandFacade;
import com.cosfo.oms.facade.aftersale.OrderAfterSaleQueryFacade;
import com.cosfo.oms.facade.dto.tenant.TenantInputQueryDTO;
import com.cosfo.oms.facade.order.OrderAddressQueryFacade;
import com.cosfo.oms.facade.order.OrderItemCommandFacade;
import com.cosfo.oms.facade.order.OrderItemQueryFacade;
import com.cosfo.oms.facade.order.OrderItemSnapshotQueryFacade;
import com.cosfo.oms.market.mapper.MarketItemMapper;
import com.cosfo.oms.market.model.po.MarketItem;
import com.cosfo.oms.merchant.service.MerchantStoreAccountService;
import com.cosfo.oms.merchant.service.MerchantStoreGroupService;
import com.cosfo.oms.merchant.service.MerchantStoreService;
import com.cosfo.oms.model.dto.LoginContextInfoDTO;
import com.cosfo.oms.order.model.converter.OrderAddressConverter;
import com.cosfo.oms.order.model.converter.OrderAfterSaleConverter;
import com.cosfo.oms.order.model.dto.OrderAfterSaleAuditDTO;
import com.cosfo.oms.order.model.dto.OrderAfterSaleBizDTO;
import com.cosfo.oms.order.model.dto.OrderAfterSaleQueryDTO;
import com.cosfo.oms.order.model.dto.OrderAfterSaleUnAuditModifyQuantityDTO;
import com.cosfo.oms.order.model.po.OrderQueryDTO;
import com.cosfo.oms.order.model.vo.OrderInfoVO;
import com.cosfo.oms.order.model.vo.OrderItemVO;
import com.cosfo.oms.order.model.vo.OrderVO;
import com.cosfo.oms.order.model.vo.aftersale.OrderAfterSaleInfoVO;
import com.cosfo.oms.order.model.vo.aftersale.OrderAfterSaleProductVO;
import com.cosfo.oms.order.model.vo.aftersale.OrderAfterSaleVO;
import com.cosfo.oms.order.service.OrderAfterSaleDomainService;
import com.cosfo.oms.payment.PaymentService;
import com.cosfo.oms.service.CommonService;
import com.cosfo.oms.tenant.model.vo.TenantVO;
import com.cosfo.oms.tenant.service.TenantInfoService;
import com.cosfo.oms.tenant.service.TenantService;
import com.cosfo.ordercenter.client.common.*;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.req.*;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleEnableResp;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleWithOrderResp;
import com.cosfo.ordercenter.client.resp.order.*;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum.RequestSource;
import net.summerfarm.common.client.enums.DownloadCenterEnum.Status;
import net.summerfarm.goods.client.enums.CategoryTypeEnum;
import net.summerfarm.goods.client.resp.CategoryResp;
import net.summerfarm.goods.client.resp.ProductSkuDetailResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.download.support.core.DownloadCenterHelper;
import net.xianmu.download.support.dto.DownloadCenterOssRespDTO;
import net.xianmu.download.support.dto.DownloadCenterRecordDTO;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import net.xianmu.usercenter.client.tenant.resp.TenantAndBusinessInfoResultResp;
import net.xianmu.usercenter.client.tenant.resp.TenantResultResp;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2022/8/25 18:25
 */
@Slf4j
@Service
public class OrderAfterSaleDomainServiceImpl implements OrderAfterSaleDomainService {

    @Resource
    private MerchantStoreService merchantStoreService;
    @Resource
    private PaymentService paymentService;
    @Resource
    private MerchantStoreAccountService merchantStoreAccountService;
    @Resource
    private TenantService tenantService;
    @Resource
    private CommonService commonService;
    @Resource
    private MarketItemMapper marketItemMapper;
    @Resource
    private MerchantStoreGroupService merchantStoreGroupService;
    @Resource
    private BillProfitSharingOrderMapper billProfitSharingOrderMapper;
    @Resource
    private TenantInfoService tenantInfoService;
    @Resource
    private CategoryServiceFacade categoryServiceFacade;
    @Resource
    private ProductQueryFacade productQueryFacade;
    @DubboReference
    private OrderAfterSaleProvider orderAfterSaleProvider;
    @Resource
    private BillProfitSharingOrderService billProfitSharingOrderService;
    @Resource
    private OrderQueryFacade orderQueryFacade;
    @Resource
    private OrderItemSnapshotQueryFacade orderItemSnapshotQueryFacade;
    @Resource
    private OrderItemQueryFacade orderItemQueryFacade;
    @Resource
    private OrderItemCommandFacade orderItemCommandFacade;
    @Resource
    private OrderAddressQueryFacade orderAddressQueryFacade;
    @Resource
    private OrderAfterSaleQueryFacade orderAfterSaleQueryFacade;
    @Resource
    private OrderAfterSaleCommandFacade orderAfterSaleCommandFacade;
    @Resource
    private MarketFacade marketFacade;

    /**
     * 待客户审核状态
     */
    private static final Integer WAIT_BRAND_AUDIT = 13;

    @Override
    public CommonResult reviewSubmissions(OrderAfterSaleAuditDTO orderAfterSaleAuditDTO, LoginContextInfoDTO loginContextInfoDTO) {
        // 数据校验
        List<OrderAfterSaleResp> orderAfterSaleResps = orderAfterSaleQueryFacade.queryByNos(Lists.newArrayList(orderAfterSaleAuditDTO.getAfterSaleOrderNo()));
        if (CollectionUtils.isEmpty(orderAfterSaleResps)) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "售后单不存在");
        }
        OrderAfterSaleResp afterSale = orderAfterSaleResps.get(0);

        OrderResp order = orderQueryFacade.queryById(afterSale.getOrderId());
        if (Objects.equals(order.getWarehouseType(), WarehouseTypeEnum.PROPRIETARY.getCode())) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, ResultDTOEnum.NO_PERMISSION_REVIEW.getMessage());
        }
        if (!Objects.equals(afterSale.getStatus(), OrderAfterSaleStatusEnum.UNAUDITED.getValue())) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, ResultDTOEnum.STATUS_NOT_AUDITING.getMessage());
        }
        if (Objects.isNull(orderAfterSaleAuditDTO.getTotalPrice())) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, ResultDTOEnum.TOTAL_PRICE_EMPTY.getMessage());
        }
        if (orderAfterSaleAuditDTO.getTotalPrice().compareTo(BigDecimal.ZERO) == NumberConstants.NEGATIVE_ONE) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, ResultDTOEnum.TOTAL_PRICE_NEGATIVE.getMessage());
        }
        if (orderAfterSaleAuditDTO.getTotalPrice().compareTo(afterSale.getApplyPrice()) == NumberConstants.ONE) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, ResultDTOEnum.TOTAL_PRICE_TOO_MUCH.getMessage());
        }

        OrderAfterSaleAuditReq req = new OrderAfterSaleAuditReq();
        req.setSystemSource(SystemSourceEnum.OMS.getCode());
        req.setAfterSaleOrderNo(orderAfterSaleAuditDTO.getAfterSaleOrderNo());
        req.setHandleRemark(orderAfterSaleAuditDTO.getHandleRemark());
        req.setAuditStatus(orderAfterSaleAuditDTO.getAuditStatus());
        req.setRecycleTime(orderAfterSaleAuditDTO.getRecycleTime());
        req.setAmount(orderAfterSaleAuditDTO.getAmount());
        req.setTotalPrice(orderAfterSaleAuditDTO.getTotalPrice());
        req.setOperatorName(loginContextInfoDTO.getUserName());
        req.setResponsibilityType(orderAfterSaleAuditDTO.getResponsibilityType());
        Boolean flag = orderAfterSaleCommandFacade.reviewSubmissions(req);
        if (!flag) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "售后单审核失败");
        }

        return CommonResult.ok();
    }

    @Override
    public CommonResult modifyQuantity(OrderAfterSaleUnAuditModifyQuantityDTO orderAfterSaleUnAuditModifyQuantityDTO, LoginContextInfoDTO loginContextInfoDTO) {
        Integer quantity = orderAfterSaleUnAuditModifyQuantityDTO.getQuantity();
        if (quantity == null || quantity <= 0) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "售后数量不能为0");
        }
        List<OrderAfterSaleResp> orderAfterSaleResps = orderAfterSaleQueryFacade.queryByNos(Lists.newArrayList(orderAfterSaleUnAuditModifyQuantityDTO.getAfterSaleOrderNo()));
        if (CollectionUtils.isEmpty(orderAfterSaleResps)) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "售后订单不存在");
        }
        OrderAfterSaleResp afterSale = orderAfterSaleResps.get(0);
        OrderResp order = orderQueryFacade.queryById(afterSale.getOrderId());
        if (order == null) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "订单不存在");
        }
        if (!Objects.equals(afterSale.getStatus(), OrderAfterSaleStatusEnum.UNAUDITED.getValue()) && !Objects.equals(afterSale.getStatus(), OrderAfterSaleStatusEnum.WAIT_CONFIRM.getValue())) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "该售后订单非待审核或者待确认，无法修改数量");
        }

        // 校验权限
        checkModifyQuantityPermission(afterSale);

        // 当前售后订单锁定的售后数量
        Integer afterSaleAmount = afterSale.getAmount();
        // 当前售后订单锁定的售后金额
        BigDecimal afterSaleApplyPrice = afterSale.getApplyPrice();

        // 校验可售后额度
        OrderAfterSaleEnableApplyReq req = new OrderAfterSaleEnableApplyReq();
        req.setTenantId(afterSale.getTenantId());
        req.setOrderId(afterSale.getOrderId());
        req.setOrderItemId(afterSale.getOrderItemId());
        Map<Long, OrderAfterSaleEnableResp> afterSaleEnableRespMap = orderAfterSaleQueryFacade.queryEnableApply(req);
        OrderAfterSaleEnableResp orderAfterSaleEnableApplyDTO = Optional.ofNullable(afterSaleEnableRespMap).map(e -> e.get(afterSale.getOrderItemId())).orElse(null);
        if (orderAfterSaleEnableApplyDTO == null) {
            throw new BizException("查询可售后信息不存在");
        }

        Integer enableApplyQuantity = orderAfterSaleEnableApplyDTO.getEnableApplyQuantity();
        Integer enableApplyAmount = orderAfterSaleEnableApplyDTO.getEnableApplyAmount();
        BigDecimal enableApplyPrice = orderAfterSaleEnableApplyDTO.getEnableApplyPrice();
        log.info("enableApplyQuantity：{}, enableApplyPrice：{}", enableApplyQuantity, enableApplyPrice);
        if (enableApplyQuantity < 0) {
            throw new BizException("可售后件数不足");
        }
        if (enableApplyPrice.compareTo(BigDecimal.ZERO) < 0) {
            throw new BizException("可售后金额不足,最大可售后金额为" + enableApplyPrice + "元");
        }

        BigDecimal applyPrice = BigDecimal.ZERO;

        boolean isReceivedRefund = OrderAfterSaleServiceTypeEnum.verifyIsReceivedRefund(afterSale.getServiceType(), afterSale.getAfterSaleType());
        if (isReceivedRefund) {
            if (quantity.compareTo(enableApplyQuantity + afterSaleAmount) > 0) {
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "可售后件数不足");
            }
            Long orderItemId = afterSale.getOrderItemId();
//            OrderItemSnapshotDTO orderItemSnapshotDTO = getOrderItemSnapshotDTO(orderItemId);
            OrderItemSnapshotResp orderItemSnapshotResp = orderItemSnapshotQueryFacade.queryByOrderItemId(orderItemId);
            OrderItemResp orderItemResp = orderItemQueryFacade.queryById(orderItemId);
//            OrderItemDTO orderItemDTO = getOrderItemDTO(orderItemId);
            if (orderItemSnapshotResp == null || orderItemResp == null) {
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "订单不存在");
            }

            // 退款-申请售后金额
            applyPrice = NumberUtil.div(NumberUtil.mul(quantity, orderItemResp.getPayablePrice()), orderItemSnapshotResp.getMaxAfterSaleAmount(), 2);

        } else {
            if (quantity.compareTo(enableApplyAmount + afterSaleAmount) > 0) {
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "可售后件数不足");
            }

//            boolean returnRefundFlag = Arrays.asList(OrderAfterSaleServiceTypeEnum.RETURN_REFUND.getValue(), OrderAfterSaleServiceTypeEnum.RETURN_REFUND_ENTER_BILL.getValue(),
//                    OrderAfterSaleServiceTypeEnum.RETURN_REFUND_BALANCE.getValue()).contains(afterSale.getServiceType());
            if (OrderAfterSaleServiceTypeEnum.verifyIsReturnRefund(afterSale.getServiceType())) {
//                OrderItemDTO orderItemDTO = getOrderItemDTO(afterSale.getOrderItemId());
                OrderItemResp orderItemResp = orderItemQueryFacade.queryById(afterSale.getOrderItemId());
                // 退货退款-申请售后金额
                applyPrice = NumberUtil.mul(orderItemResp.getPayablePrice(), quantity);
            }
        }
        if (applyPrice.compareTo(NumberUtil.add(enableApplyPrice, afterSaleApplyPrice)) > 0) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "可售后金额不足,最大可售后金额为" + enableApplyPrice + "元");
        }

        OrderAfterSaleModifyQuantityReq update = new OrderAfterSaleModifyQuantityReq();
        update.setAfterSaleId(afterSale.getId());
        update.setOperatorName(loginContextInfoDTO.getUserName());
        update.setUpdateTime(LocalDateTime.now());
        update.setAmount(quantity);
        update.setApplyPrice(applyPrice);
        update.setTotalPrice(applyPrice);

        return CommonResult.ok(orderAfterSaleCommandFacade.modifyQuantity(update));
    }

    private void checkModifyQuantityPermission(OrderAfterSaleResp afterSale) {
        // 非三方仓不支持改数量
        if (!Objects.equals(afterSale.getWarehouseType(), com.cosfo.ordercenter.client.common.WarehouseTypeEnum.THREE_PARTIES.getCode())) {
            throw new BizException("该售后订单您暂无改数量权限");
        }
        // 代仓售后且品牌方开启自审配置
//        if (Objects.equals(afterSale, GoodsTypeEnum.SELF_GOOD_TYPE.getCode()) && orderAfterSaleAgentSelfReviewConfig.getAgentAfterSaleSelfReviewFlag()) {
//            throw new BizException("该售后订单您暂无改数量权限，品牌方开启了代仓权限");
//        }
    }

    @Override
    public CommonResult cancel(OrderAfterSaleBizDTO orderAfterSaleDTO, LoginContextInfoDTO merchantInfoDTO) {
        Long id = orderAfterSaleDTO.getId();
        List<OrderAfterSaleResp> orderAfterSaleResps = orderAfterSaleQueryFacade.queryByIds(Collections.singletonList(id));
        if (CollectionUtils.isEmpty(orderAfterSaleResps)) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "售后单不存在");
        }
        OrderAfterSaleResp orderAfterSale = orderAfterSaleResps.get(0);
        OrderResp order = orderQueryFacade.queryById(orderAfterSale.getOrderId());
        if (Objects.equals(order.getWarehouseType(), WarehouseTypeEnum.PROPRIETARY.getCode())) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, ResultDTOEnum.NO_PERMISSION_CANCEL.getMessage());
        }
        if (Objects.equals(orderAfterSale.getStatus(), OrderAfterSaleStatusEnum.UNAUDITED.getValue())
                || Objects.equals(orderAfterSale.getStatus(), OrderAfterSaleStatusEnum.REFUNDING.getValue())
                || Objects.equals(orderAfterSale.getStatus(), OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue())
                || Objects.equals(orderAfterSale.getStatus(), OrderAfterSaleStatusEnum.AUDITED_FAILED.getValue())
                || Objects.equals(orderAfterSale.getStatus(), OrderAfterSaleStatusEnum.CANCEL.getValue())
                || Objects.equals(orderAfterSale.getStatus(), OrderAfterSaleStatusEnum.WAIT_CONFIRM.getValue())
        ) {
            throw new BizException("该售后单已不可以关闭");
        }

        Boolean cancel = orderAfterSaleCommandFacade.cancel(id);
        if (Boolean.FALSE.equals(cancel)) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "售后单取消失败");
        }

        log.info("管理员:{}, 取消了售后订单:{}", merchantInfoDTO.getUserName(), orderAfterSale.getAfterSaleOrderNo());
        return CommonResult.ok();
    }

    @Override
    public CommonResult refund(OrderAfterSaleBizDTO orderAfterSaleDTO, LoginContextInfoDTO loginContextInfoDTO) {
        if (Objects.isNull(orderAfterSaleDTO.getApplyPrice())) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "请输入售后金额");
        }
        if (orderAfterSaleDTO.getApplyPrice().compareTo(BigDecimal.ZERO) < NumberConstants.ZERO) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "售后金额不得小于0");
        }

        Long id = orderAfterSaleDTO.getId();
        List<OrderAfterSaleResp> orderAfterSaleResps = orderAfterSaleQueryFacade.queryByIds(Collections.singletonList(id));
        if (CollectionUtils.isEmpty(orderAfterSaleResps)) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "售后单不存在");
        }

        OrderAfterSaleResp orderAfterSale = orderAfterSaleResps.get(0);
        OrderResp order = orderQueryFacade.queryById(orderAfterSale.getOrderId());
        if (Objects.equals(order.getWarehouseType(), WarehouseTypeEnum.PROPRIETARY.getCode())) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, ResultDTOEnum.NO_PERMISSION_REFUND.getMessage());
        }
        if (!Objects.equals(orderAfterSale.getStatus(), OrderAfterSaleStatusEnum.WAIT_REFUND.getValue())) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, ResultDTOEnum.ORDER_AFTER_SALE_STATUS_ERROR.getMessage());
        }

        // 校验售后金额
        verifyApplyPriceAndAmount(id, orderAfterSaleDTO.getApplyPrice(), orderAfterSale.getAmount());

        OrderAfterSaleRecycleFailRefundReq update = new OrderAfterSaleRecycleFailRefundReq();
        update.setAfterSaleId(orderAfterSale.getId());
        update.setTotalPrice(orderAfterSaleDTO.getApplyPrice());
        orderAfterSaleCommandFacade.recycleFailRefund(update);
        // 发起退款
        paymentService.payRefund(orderAfterSale.getId(), orderAfterSale.getOrderId(), orderAfterSale.getTenantId(), orderAfterSaleDTO.getApplyPrice());
        log.info("管理员:{}对售后订单:{}发起了退款，退款金额为{}", loginContextInfoDTO.getUserName(), orderAfterSale.getAfterSaleOrderNo(), orderAfterSaleDTO.getApplyPrice());

        return CommonResult.ok();
    }

    @Override
    public void verifyApplyPriceAndAmount(Long id, BigDecimal applyPrice, Integer applyAmount) {
        List<OrderAfterSaleResp> orderAfterSaleResps = orderAfterSaleQueryFacade.queryByIds(Collections.singletonList(id));
        if (CollectionUtils.isEmpty(orderAfterSaleResps)) {
            throw new BizException("售后单不存在");
        }
        OrderAfterSaleResp orderAfterSale = orderAfterSaleResps.get(0);
        OrderAfterSaleEnableApplyReq req = new OrderAfterSaleEnableApplyReq();
        req.setTenantId(orderAfterSale.getTenantId());
        req.setOrderId(orderAfterSale.getOrderId());
        req.setOrderItemId(orderAfterSale.getOrderItemId());

        Map<Long, OrderAfterSaleEnableResp> afterSaleEnableRespMap = orderAfterSaleQueryFacade.queryEnableApply(req);

        OrderAfterSaleEnableResp orderAfterSaleEnableApplyDTO = Optional.ofNullable(afterSaleEnableRespMap).map(e -> e.get(orderAfterSale.getOrderItemId())).orElse(null);
        if (orderAfterSaleEnableApplyDTO == null) {
            throw new BizException("查询可售后信息不存在");
        }

        Integer enableApplyQuantity = orderAfterSaleEnableApplyDTO.getEnableApplyQuantity();
        BigDecimal enableApplyPrice = orderAfterSaleEnableApplyDTO.getEnableApplyPrice();
        log.info("enableApplyQuantity：{}, enableApplyPrice：{}", enableApplyQuantity, enableApplyPrice);
        if (enableApplyQuantity < 0) {
            throw new DefaultServiceException("可售后件数不足");
        }
        if (enableApplyPrice.compareTo(BigDecimal.ZERO) < 0) {
            throw new DefaultServiceException("可售后金额不足,最大可售后金额为" + enableApplyPrice + "元");
        }
    }

    @Override
    public CommonResult<Long> save(OrderAfterSaleBizDTO orderAfterSaleDTO, LoginContextInfoDTO loginContextInfoDTO) {
        // 只能是已到货售后
        if (Objects.equals(orderAfterSaleDTO.getAfterSaleType(), OrderAfterSaleTypeEnum.NOT_SEND.getType())) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "暂时不支持发起未到货售后");
        }
        Long orderItemId = orderAfterSaleDTO.getOrderItemId();
//        OrderItemDTO orderItemDTO = getOrderItemDTO(orderItemId);
        OrderItemResp orderItemResp = orderItemQueryFacade.queryById(orderItemId);
        orderAfterSaleDTO.setTenantId(orderItemResp.getTenantId());
        OrderResp order = orderQueryFacade.queryById(orderItemResp.getOrderId());
//        OrderDTO order = getOrder(orderItemDTO.getOrderId());
        if (!Objects.equals(order.getStatus(), OrderStatusEnum.DELIVERING.getCode()) && !Objects.equals(order.getStatus(), OrderStatusEnum.FINISHED.getCode())) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "该订单暂时不支持发起已到货售后");
        }
        orderAfterSaleDTO.setWarehouseType(order.getWarehouseType());
        orderAfterSaleDTO.setOrderId(ObjectUtil.isNotNull(orderAfterSaleDTO.getOrderId()) ? orderAfterSaleDTO.getOrderId() : orderItemResp.getOrderId());

        // 可以发起，暂时不能审核
//        if (Objects.equals(orderAfterSaleDTO.getAfterSaleType(), OrderAfterSaleTypeEnum.DELIVERED.getType()) && verifyIsOnlineRefund(orderAfterSaleDTO.getServiceType()) && !checkCanCreateAfterSaleOverdue(order.getTenantId(), order.getId())) {
//            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "该订单正在分账中，暂时不能发起退款审核, 请稍后重试");
//        }

        orderAfterSaleDTO.setReqSource("oms");

        Long recordId = null;
        try {
            OrderAfterSaleAddReq afterSaleAddReq = OrderAfterSaleConverter.INSTANCE.bizDto2Req(orderAfterSaleDTO);
            recordId = orderAfterSaleCommandFacade.createAfterDeliveryAfterSale(afterSaleAddReq);
        } catch (Exception e) {
            log.error("发起售后失败", e);
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, e.getMessage());
        }

        if (orderItemResp.getAfterSaleExpiryTime() != null && orderItemResp.getAfterSaleExpiryTime().isBefore(LocalDateTime.now())) {
            OrderItemUpdateReq updateDTO = new OrderItemUpdateReq();
            updateDTO.setOrderItemId(orderItemResp.getId());
            updateDTO.setAfterSaleExpiryTime(LocalDateTime.now());
            orderItemCommandFacade.updateAfterSaleExpiryTime(updateDTO);
        }
        return CommonResult.ok(recordId);
    }

    /**
     * 校验是否是在线支付
     *
     * @param serviceType
     * @return
     */
    private boolean verifyIsOnlineRefund(Integer serviceType) {
        if (OrderAfterSaleServiceTypeEnum.REFUND.getValue().equals(serviceType) || OrderAfterSaleServiceTypeEnum.RETURN_REFUND.getValue().equals(serviceType)) {
            return true;
        }

        return false;
    }

    /**
     * 检查超期后是否可以发起售后
     *
     * @param orderId
     * @return
     */
    private boolean checkCanCreateAfterSaleOverdue(Long tenantId, Long orderId) {
        BillProfitSharingOrder billProfitSharingOrder = billProfitSharingOrderMapper.queryByOrderIdAndTenantId(tenantId, orderId);
        // 分账状态0待分账1分账处理中2部分分账3分账完成4分账失败
        Integer status = 3;
        if (Objects.nonNull(billProfitSharingOrder) && status.equals(billProfitSharingOrder.getStatus())) {
            return true;
        }

        return false;
    }

    @Override
    public CommonResult<PageInfo<OrderAfterSaleInfoVO>> getOrderListAfterSale(OrderAfterSaleQueryDTO orderAfterSaleQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        // 处理查询条件
        Boolean flag = dealQueryConditions(orderAfterSaleQueryDTO);
        if (!flag) {
//            return PageResultDTO.success(PageInfoHelper.createPageInfo(new ArrayList<>(), orderAfterSaleQueryDTO.getPageSize()));
            return CommonResult.ok(PageInfo.emptyPageInfo());
        }

        if (CollectionUtils.isEmpty(orderAfterSaleQueryDTO.getItemIds())) {
            orderAfterSaleQueryDTO.setItemIds(Collections.emptyList());
        }
        // 查询订单数据
//        PageHelper.startPage(orderAfterSaleQueryDTO.getPageNum(), orderAfterSaleQueryDTO.getPageSize());
//        List<OrderAfterSaleInfoVO> afterSaleInfoVOS = selectByOrderQueryDTO(orderAfterSaleQueryDTO);
//        return PageResultDTO.success(PageInfoHelper.createPageInfo(afterSaleInfoVOS, orderAfterSaleQueryDTO.getPageSize()));

        return CommonResult.ok(selectByOrderQueryDTO(orderAfterSaleQueryDTO));
    }

    private PageInfo<OrderAfterSaleInfoVO> selectByOrderQueryDTO(OrderAfterSaleQueryDTO orderAfterSaleQueryDTO) {
        OrderAfterSalePageQueryReq req = new OrderAfterSalePageQueryReq();
        req.setOrderNo(orderAfterSaleQueryDTO.getOrderNo());
        req.setAfterSaleOrderNo(orderAfterSaleQueryDTO.getAfterSaleOrderNo());
        if (orderAfterSaleQueryDTO.getStatus() != null && orderAfterSaleQueryDTO.getStatus() == 2) {
            req.setStatusList(Lists.newArrayList(OrderAfterSaleStatusEnum.INVENTORY_DEALING.getValue(), OrderAfterSaleStatusEnum.THIRD_PROCESSING.getValue(),
                    OrderAfterSaleStatusEnum.WAIT_REFUND_GOODS.getValue(), OrderAfterSaleStatusEnum.REFUNDDING_GOODS.getValue()));
        } else if (orderAfterSaleQueryDTO.getStatus() != null) {
            req.setStatusList(Lists.newArrayList(orderAfterSaleQueryDTO.getStatus()));
        }
        req.setAfterSaleType(orderAfterSaleQueryDTO.getAfterSaleType());
        if (orderAfterSaleQueryDTO.getServiceType() != null) {
            req.setServiceTypeList(Lists.newArrayList(orderAfterSaleQueryDTO.getServiceType()));
        }
        if (orderAfterSaleQueryDTO.getPayType() != null) {
            req.setPayTypes(Lists.newArrayList(orderAfterSaleQueryDTO.getPayType()));
        }
        req.setStoreIds(orderAfterSaleQueryDTO.getStoreIds());
        req.setAccountIds(orderAfterSaleQueryDTO.getAccountIds());
        req.setStartTime(LocalDateTimeUtil.of(orderAfterSaleQueryDTO.getStartTime()));
        req.setEndTime(LocalDateTimeUtil.of(orderAfterSaleQueryDTO.getEndTime()));
        req.setSupplierTenantId(orderAfterSaleQueryDTO.getSupplierTenantId());
        req.setTenantIds(orderAfterSaleQueryDTO.getTenantIds());
        req.setPageNum(orderAfterSaleQueryDTO.getPageNum());
        req.setPageSize(orderAfterSaleQueryDTO.getPageSize());
        req.setMaxId(orderAfterSaleQueryDTO.getMaxPrimaryId());
        req.setItemIds(orderAfterSaleQueryDTO.getItemIds());

        PageInfo<OrderAfterSaleWithOrderResp> orderAfterSalRespPageInfo = orderAfterSaleQueryFacade.queryPage(req);
        if (orderAfterSalRespPageInfo == null || CollectionUtils.isEmpty(orderAfterSalRespPageInfo.getList())) {
            return PageInfo.emptyPageInfo();
        }
        List<OrderAfterSaleWithOrderResp> orderAfterSaleDTOS = orderAfterSalRespPageInfo.getList();
        List<OrderAfterSaleInfoVO> afterSaleInfoVOS = OrderAfterSaleConverter.INSTANCE.convertresps2VOS(orderAfterSaleDTOS);


        // 在补充门店、账户、商城信息
        List<Long> storeIds = afterSaleInfoVOS.stream().filter(vo -> Objects.nonNull(vo.getStoreId())).map(OrderAfterSaleInfoVO::getStoreId).distinct().collect(Collectors.toList());
        List<Long> accountIds = afterSaleInfoVOS.stream().filter(vo -> Objects.nonNull(vo.getAccountId())).map(OrderAfterSaleInfoVO::getAccountId).distinct().collect(Collectors.toList());
        List<Long> tenantIds = afterSaleInfoVOS.stream().filter(vo -> Objects.nonNull(vo.getTenantId())).map(OrderAfterSaleInfoVO::getTenantId).distinct().collect(Collectors.toList());
        List<Long> orderIds = afterSaleInfoVOS.stream().filter(vo -> Objects.nonNull(vo.getOrderId())).map(OrderAfterSaleInfoVO::getOrderId).distinct().collect(Collectors.toList());
        List<TenantResultResp> tenantInfoList = tenantInfoService.getTenantInfoList(tenantIds);
        Map<Long, TenantResultResp> tenantIdMap = tenantInfoList.stream().collect(Collectors.toMap(TenantResultResp::getId, Function.identity(), (v1, v2) -> v1));
        Map<Long, MerchantStoreResultResp> storeIdMap = merchantStoreService.getMerchantStoreMap(storeIds);
        Map<Long, MerchantStoreAccountResultResp> accountIdMap = merchantStoreAccountService.getAccountMap(accountIds);

        List<OrderResp> orderResps = orderQueryFacade.queryByIds(orderIds);
        Map<Long, String> orderIdToOrderNoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(orderResps)) {
            orderIdToOrderNoMap = orderResps.stream()
                    .collect(Collectors.toMap(OrderResp::getId, OrderResp::getOrderNo));
        }


        for (OrderAfterSaleInfoVO afterSaleInfoVO : afterSaleInfoVOS) {
            TenantResultResp tenantResultResp = tenantIdMap.get(afterSaleInfoVO.getTenantId());
            MerchantStoreResultResp merchantStoreResultResp = storeIdMap.get(afterSaleInfoVO.getStoreId());
            MerchantStoreAccountResultResp merchantStoreAccountResultResp = accountIdMap.get(afterSaleInfoVO.getAccountId());
            afterSaleInfoVO.setTenantName(Optional.ofNullable(tenantResultResp).map(TenantResultResp::getTenantName).orElse(null));
            afterSaleInfoVO.setStoreName(Optional.ofNullable(merchantStoreResultResp).map(MerchantStoreResultResp::getStoreName).orElse(null));
            afterSaleInfoVO.setStoreType(Optional.ofNullable(merchantStoreResultResp).map(MerchantStoreResultResp::getType).orElse(null));
            afterSaleInfoVO.setPhone(Optional.ofNullable(merchantStoreAccountResultResp).map(MerchantStoreAccountResultResp::getPhone).orElse(null));
            afterSaleInfoVO.setAccountName(Optional.ofNullable(merchantStoreAccountResultResp).map(MerchantStoreAccountResultResp::getAccountName).orElse(null));

            if (afterSaleInfoVO.getOrderId() != null && orderIdToOrderNoMap.containsKey(afterSaleInfoVO.getOrderId())) {
                afterSaleInfoVO.setOrderNo(orderIdToOrderNoMap.get(afterSaleInfoVO.getOrderId()));
            }
            afterSaleInfoVO.setStatus(getFrontShowStatus(afterSaleInfoVO.getTenantId(), afterSaleInfoVO.getStatus(), afterSaleInfoVO.getWarehouseType(), afterSaleInfoVO.getGoodsType()));
        }
        PageInfo<OrderAfterSaleInfoVO> resultPageInfo = PageInfoHelper.pageInfoCopy(orderAfterSalRespPageInfo, afterSaleInfoVOS);
        return resultPageInfo;
    }

    private Integer getFrontShowStatus(Long tenantId, Integer status, Integer warehouseType, Integer goodsType) {
        if (Objects.equals(status, OrderAfterSaleStatusEnum.THIRD_PROCESSING.getValue())) {
            return OrderAfterSaleStatusEnum.INVENTORY_DEALING.getValue();
        }
        // 代仓且开启自审加待审核显示待客户审核
        OrderAfterSaleSelfReviewAgentReq req = new OrderAfterSaleSelfReviewAgentReq(tenantId);
        Boolean needSelfReviewFlag = RpcResultUtil.handle(orderAfterSaleProvider.needSelfReviewFlag(req));
        if (Objects.equals(goodsType, GoodsTypeEnum.SELF_GOOD_TYPE.getCode())
                && Objects.equals(warehouseType, WarehouseTypeEnum.THREE_PARTIES.getCode())
                && needSelfReviewFlag
                && Objects.equals(status, OrderAfterSaleStatusEnum.UNAUDITED.getValue())
        ) {
            return WAIT_BRAND_AUDIT;
        }
        return status;
    }

    private PageInfo<OrderAfterSaleInfoVO> selectByOrderQueryDTOWithProofPicture(OrderAfterSaleQueryDTO orderAfterSaleQueryDTO) {
        PageInfo<OrderAfterSaleInfoVO> pageInfo = selectByOrderQueryDTO(orderAfterSaleQueryDTO);
        // 把proof picture分开,导出多条记录
        List<OrderAfterSaleInfoVO> expandedAfterSaleInfoVOS = new ArrayList<>();
        for (OrderAfterSaleInfoVO vo : pageInfo.getList()) {
            String proofPictures = vo.getProofPicture();

            if (StringUtils.isEmpty(proofPictures)) {
                expandedAfterSaleInfoVOS.add(vo);
                continue;
            }
            String[] pictureUrls = proofPictures.split(",");
            if (pictureUrls.length == 0) {
                expandedAfterSaleInfoVOS.add(vo);
                continue;
            }

            for (String url : pictureUrls) {
                String completeUrl = "https://azure.cosfo.cn/" + url;
                OrderAfterSaleInfoVO newVO = vo.toBuilder().proofPicture(completeUrl).build();
                expandedAfterSaleInfoVOS.add(newVO);
            }
        }
        pageInfo.setList(expandedAfterSaleInfoVOS);
        return pageInfo;
    }

    /**
     * 处理查询条件
     *
     * @param orderAfterSaleQueryDTO
     */
    private Boolean dealQueryConditions(OrderAfterSaleQueryDTO orderAfterSaleQueryDTO) {
        orderAfterSaleQueryDTO.setSupplierTenantId(SupplierTenantConstant.SUMMERFARM_TENANT_ID);
        List<Long> storeIds = new ArrayList<>();
        // 门店类型和门店账号
        if (orderAfterSaleQueryDTO.getStoreType() != null || !StringUtils.isEmpty(orderAfterSaleQueryDTO.getStoreName())) {
            OrderQueryDTO orderQueryDTO = new OrderQueryDTO();
            orderQueryDTO.setStoreName(orderAfterSaleQueryDTO.getStoreName());
            orderQueryDTO.setStoreType(orderAfterSaleQueryDTO.getStoreType());
            // 查询门店信息
            List<MerchantStoreResultResp> merchantStores = merchantStoreService.getMerchantStoreList(orderQueryDTO.getStoreType(), orderQueryDTO.getStoreName());
            storeIds = merchantStores.stream().map(MerchantStoreResultResp::getId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(storeIds)) {
                return Boolean.FALSE;
            }
        }


        orderAfterSaleQueryDTO.setStoreIds(storeIds);
        List<Long> accountIds = new ArrayList<>();
        // 注册手机号
        if (!org.springframework.util.StringUtils.isEmpty(orderAfterSaleQueryDTO.getPhone())) {
            orderAfterSaleQueryDTO.setPhone(orderAfterSaleQueryDTO.getPhone());
            List<MerchantStoreAccountResultResp> merchantStoreAccounts = merchantStoreAccountService.queryMerchantStoreAccountByPhone(orderAfterSaleQueryDTO.getPhone());
            accountIds = merchantStoreAccounts.stream().map(MerchantStoreAccountResultResp::getId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(accountIds)) {
                return Boolean.FALSE;
            }
        }
        if (null != orderAfterSaleQueryDTO.getEndTime() && null != orderAfterSaleQueryDTO.getStartTime()) {
            orderAfterSaleQueryDTO.setEndTime(DateUtil.dayEnd(orderAfterSaleQueryDTO.getEndTime()));
            orderAfterSaleQueryDTO.setStartTime(DateUtil.zero(orderAfterSaleQueryDTO.getStartTime()));
        }
        orderAfterSaleQueryDTO.setAccountIds(accountIds);

        // 模糊查询商城名称匹配出商城ID列表
        if (!StringUtils.isEmpty(orderAfterSaleQueryDTO.getTenantName())) {
            List<TenantAndBusinessInfoResultResp> tenantInfos = tenantInfoService.getTenantAndCompanyInfoList(TenantInputQueryDTO.builder().tenantName(orderAfterSaleQueryDTO.getTenantName()).build());
            if (CollectionUtils.isEmpty(tenantInfos)) {
                return Boolean.FALSE;
            }
            orderAfterSaleQueryDTO.setTenantIds(tenantInfos.stream().map(TenantAndBusinessInfoResultResp::getTenantId).collect(Collectors.toList()));
        }

        //商品条件
        orderAfterSaleQueryDTO.setItemIds(new ArrayList<>());
        if (Objects.nonNull(orderAfterSaleQueryDTO.getItemId()) || Objects.nonNull(orderAfterSaleQueryDTO.getTitle())) {
            Long queryTenantId = Optional.ofNullable(orderAfterSaleQueryDTO.getTenantIds()).filter(list -> list.size() == 1).map(list -> list.get(0)).orElse(null);

            List<MarketItemInfoResp> itemInfoResps = marketFacade.queryMarketItemList(queryTenantId, orderAfterSaleQueryDTO.getTitle(), orderAfterSaleQueryDTO.getItemId());
            if(CollectionUtils.isEmpty(itemInfoResps)){
                return Boolean.FALSE;
            }
            orderAfterSaleQueryDTO.setItemIds(itemInfoResps.stream().map(MarketItemInfoResp::getItemId).collect(Collectors.toList()));
        }

        return Boolean.TRUE;
    }


    @Override
    public void exportOrdersAfterSale(OrderAfterSaleQueryDTO afterSaleQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        Map<String, String> queryParamsMap = new LinkedHashMap<>(NumberConstants.TEN);
        // 原订单编号
        if (!StringUtils.isEmpty(afterSaleQueryDTO.getOrderNo())) {
            queryParamsMap.put(Constants.ORDER_NO, afterSaleQueryDTO.getOrderNo());
        }
        // 售后订单状态
        if (Objects.nonNull(afterSaleQueryDTO.getStatus())) {
            queryParamsMap.put(Constants.ORDER_AFTER_SALE_STATUS, OrderAfterSaleStatusEnum.getStatusDesc(afterSaleQueryDTO.getStatus()));
        }
        // 售后类型
        if (Objects.nonNull(afterSaleQueryDTO.getAfterSaleType())) {
            queryParamsMap.put(Constants.AFTER_SALE_TYPE, OrderAfterSaleTypeEnum.getDesc(afterSaleQueryDTO.getAfterSaleType()));
        }
        // 门店类型
        if (Objects.nonNull(afterSaleQueryDTO.getStoreType())) {
            queryParamsMap.put(Constants.STORE_TYPE, StoreTypeEnum.getDesc(afterSaleQueryDTO.getStoreType()));
        }
        // 售后服务类型
        if (Objects.nonNull(afterSaleQueryDTO.getServiceType())) {
            queryParamsMap.put(Constants.SERVICE_TYPE, OrderAfterSaleServiceTypeEnum.getDesc(afterSaleQueryDTO.getServiceType()));
        }
        // 退款方式
        if (Objects.nonNull(afterSaleQueryDTO.getPayType())) {
            queryParamsMap.put(Constants.AFTER_SALE_PAY_TYPE, Optional.ofNullable(PayTypeEnum.getPayType(afterSaleQueryDTO.getPayType())).map(PayTypeEnum::getDesc).orElse(""));
        }
        // 起始时间
        if (Objects.nonNull(afterSaleQueryDTO.getStartTime())) {
            queryParamsMap.put(Constants.ORDER_START_TIME, TimeUtils.changeDate2String(afterSaleQueryDTO.getStartTime(), Constants.TIME_FORMAT_LONG));
        }
        // 售后订单编号
        if (!StringUtils.isEmpty(afterSaleQueryDTO.getAfterSaleOrderNo())) {
            queryParamsMap.put(Constants.AFTER_SALE_ORDER_NO, afterSaleQueryDTO.getAfterSaleOrderNo());
        }
        // 截止时间
        if (Objects.nonNull(afterSaleQueryDTO.getEndTime())) {
            queryParamsMap.put(Constants.ORDER_END_TIME, TimeUtils.changeDate2String(afterSaleQueryDTO.getEndTime(), Constants.TIME_FORMAT_SHORT) + Constants.END_TIME);
        }
        // 门店名称
        if (!StringUtils.isEmpty(afterSaleQueryDTO.getStoreName())) {
            queryParamsMap.put(Constants.STORE_NAME, afterSaleQueryDTO.getStoreName());
        }
        // 商城名称
        if (!StringUtils.isEmpty(afterSaleQueryDTO.getTenantName())) {
            queryParamsMap.put(Constants.TENANT_NAME, afterSaleQueryDTO.getTenantName());
        }
        // 手机号
        if (!StringUtils.isEmpty(afterSaleQueryDTO.getPhone())) {
            queryParamsMap.put(Constants.PHONE, afterSaleQueryDTO.getPhone());
        }
        if (ObjectUtil.isNotNull(afterSaleQueryDTO.getItemId())) {
            queryParamsMap.put(Constants.ITEM_ID, afterSaleQueryDTO.getItemId().toString());
        }
        if (ObjectUtil.isNotNull(afterSaleQueryDTO.getTitle())) {
            queryParamsMap.put(Constants.TITLE, afterSaleQueryDTO.getTitle());
        }

        String fileName = ExcelTypeEnum.AFTER_SALE_ORDER_EXPORT.getDesc() + TimeUtils.changeDate2String(new Date(), "yyyy-MM-dd HH_mm_ss") + ".xlsx";

        DownloadCenterRecordDTO recordDTO = new DownloadCenterRecordDTO();

        recordDTO.setSource(RequestSource.SAAS_BOSS);
        recordDTO.setBizType(FileDownloadTypeEnum.BOSS_ORDER_AFTER_SALE.getType());
        recordDTO.setTenantId(XianmuSupplyTenant.BOSS_TENANT_ID);
        recordDTO.setFileName(fileName);
        recordDTO.setParams(queryParamsMap.isEmpty() ? Constants.TOTAL : JSONObject.toJSONString(queryParamsMap));
        recordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.MONTH);
        DownloadCenterHelper.build(ExecutorFactory.generateExcelExecutor, recordDTO).asyncWriteWithOssResp(afterSaleQueryDTO, e -> writeDownloadCenter(e, fileName));
    }

    // 业务自定义的函数回调方法
    public DownloadCenterOssRespDTO writeDownloadCenter(OrderAfterSaleQueryDTO afterSaleQueryDTO, String fileName) {
        // 1、表格处理
        String filePath = generateAfterSaleOrderFile(afterSaleQueryDTO);

        // 2、文件上传至oss
        OssUploadResult uploadResult = null;
        try {
            uploadResult = OssUploadUtil.upload(fileName, FileUtils.openInputStream(new File(filePath)), OSSExpiredLabelEnum.MONTH);
        } catch (IOException e) {
            log.error("filePath={}", filePath, e);
            throw new BizException("读取文件报错");
        } finally {
            commonService.deleteFile(filePath);
        }
        // 3、返回文件地址
        DownloadCenterOssRespDTO downloadCenterOssRespDTO = new DownloadCenterOssRespDTO();
        downloadCenterOssRespDTO.setStatus(Status.UPLOADED);
        downloadCenterOssRespDTO.setOssBucketKey(uploadResult.getObjectOssKey());
        return downloadCenterOssRespDTO;
    }

    private String generateAfterSaleOrderFile(OrderAfterSaleQueryDTO afterSaleQueryDTO) {
        // 处理查询条件
        Boolean flag = dealQueryConditions(afterSaleQueryDTO);
        if (!flag) {
            String filePath = commonService.exportExcel(Collections.emptyList(), ExcelTypeEnum.AFTER_SALE_ORDER_EXPORT.getName());
            return filePath;
        }

        afterSaleQueryDTO.setPageNum(NumberConstants.ONE);
        afterSaleQueryDTO.setPageSize(NumberConstants.ONE_HUNDRED);

        PageInfo<OrderAfterSaleInfoVO> pageInfo = selectByOrderQueryDTOWithProofPicture(afterSaleQueryDTO);
        int pages = (int) Math.max((pageInfo.getTotal() - 1) / pageInfo.getPageSize() + 1, 0);

        // 创建excelWriter
        String filePath = ExcelUtils.tempExcelFilePath();
        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.FALSE).build();
        ExcelWriter excelWriter = EasyExcel.write(filePath, OrderAfterSaleInfoVO.class)
                .registerConverter(new EasyExcelLocalDateConverter())
                .registerConverter(new LocalDateTimeConverter())
                .withTemplate(ExcelUtils.getExcelFileInputStream(this.getClass(), ExcelTypeEnum.AFTER_SALE_ORDER_EXPORT.getName()))
                .build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();

        Long maxPrimaryId = null;
        // 分页查询
        for (int pageNum = NumberConstants.ONE; pageNum <= pages; pageNum++) {
            List<OrderAfterSaleInfoVO> orderAfterSaleInfoVOS;
            if (pageNum == NumberConstants.ONE) {
                orderAfterSaleInfoVOS = pageInfo.getList();
            } else {
                afterSaleQueryDTO.setMaxPrimaryId(maxPrimaryId);
                orderAfterSaleInfoVOS = selectByOrderQueryDTO(afterSaleQueryDTO).getList();
            }

            if (CollectionUtil.isEmpty(orderAfterSaleInfoVOS)) {
                break;
            }
            List<OrderAfterSaleInfoVO> tempList = orderAfterSaleInfoVOS.stream().map(orderAfterSaleInfoVO -> convertOrderAfterSaleInfoVO(orderAfterSaleInfoVO)).collect(Collectors.toList());
            maxPrimaryId = tempList.stream().min(Comparator.comparing(OrderAfterSaleInfoVO::getId)).get().getId();
            // 分批写
            excelWriter.fill(tempList, fillConfig, writeSheet);
        }
        excelWriter.finish();
        return filePath;
    }


    private OrderAfterSaleInfoVO convertOrderAfterSaleInfoVO(OrderAfterSaleInfoVO afterSaleInfo) {
        MarketItem marketItem = marketItemMapper.selectByPrimaryKey(afterSaleInfo.getItemId());
        List<Long> storeIds = Lists.newArrayList(afterSaleInfo.getStoreId());
        Map<Long, String> groupMap = merchantStoreGroupService.queryBatchByStoreIds(afterSaleInfo.getTenantId(), storeIds);

        if (OrderAfterSaleStatusEnum.THIRD_PROCESSING.getValue().equals(afterSaleInfo.getStatus())) {
            afterSaleInfo.setStatus(OrderAfterSaleStatusEnum.INVENTORY_DEALING.getValue());
        }
        afterSaleInfo.setStatusDesc(OrderAfterSaleStatusEnum.getStatusDesc(afterSaleInfo.getStatus()));
        afterSaleInfo.setAfterSaleTypeDesc(OrderAfterSaleTypeEnum.getDesc(afterSaleInfo.getAfterSaleType()));
        afterSaleInfo.setServiceTypeDesc(OrderAfterSaleServiceTypeEnum.getDesc(afterSaleInfo.getServiceType()));
        afterSaleInfo.setPayTypeDesc(PayTypeEnum.getDesc(afterSaleInfo.getPayType()));
        afterSaleInfo.setItemId(afterSaleInfo.getItemId());
        afterSaleInfo.setSkuId(Objects.equals(-1L, afterSaleInfo.getSkuId()) ? null : afterSaleInfo.getSkuId());
        afterSaleInfo.setItemCode(Objects.isNull(marketItem) ? "" : marketItem.getItemCode());
        afterSaleInfo.setAfterSaleUnit(OrderAfterSaleServiceTypeEnum.verifyIsReceivedRefund(afterSaleInfo.getServiceType(), afterSaleInfo.getAfterSaleType()) ? afterSaleInfo.getAfterSaleUnit()
                : afterSaleInfo.getSpecificationUnit());
        afterSaleInfo.setMerchantStoreGroupName(groupMap.get(afterSaleInfo.getStoreId()));
        afterSaleInfo.setStoreTypeDesc(StoreTypeEnum.getDesc(afterSaleInfo.getStoreType()));
        return afterSaleInfo;
    }


    @Override
    public CommonResult getOrderDetails(Long orderId) {
        AssertParam.notNull(orderId, ResultDTOEnum.PARAMETER_MISSING.getCode(), "订单编号不能为空");
//        OrderDTO order = getOrder(orderId);
        OrderResp order = orderQueryFacade.queryById(orderId);
        AssertBiz.notNull(order, ResultDTOEnum.PARAMETER_MISSING.getCode(), "订单不存在");
        OrderAfterSaleVO saleOrderVo = OrderMapper.INSTANCE.dtoToAfterSale(order);
        //补充店铺信息
        MerchantStoreResultResp merchantStoreInfo = merchantStoreService.getMerchantStoreInfo(saleOrderVo.getStoreId());
        if (merchantStoreInfo != null) {
            saleOrderVo.setStoreName(merchantStoreInfo.getStoreName());
            saleOrderVo.setStoreType(merchantStoreInfo.getType());
        }
        // 获取下单账号信息
        MerchantStoreAccountResultResp accountInfo = merchantStoreAccountService.getAccountInfo(saleOrderVo.getAccountId());
        StringBuffer accountName = new StringBuffer(accountInfo.getAccountName())
                .append("(").append(accountInfo.getPhone()).append(")");
        saleOrderVo.setAccountName(accountName.toString());

        // 查询租户信息
        TenantVO tenant = tenantService.queryTenantById(saleOrderVo.getTenantId(), TenantTypeEnum.BRAND.getCode());
        saleOrderVo.setTenantName(tenant.getTenantName());

        // 查询订单地址信息
        OrderAddressResp orderAddressResp = orderAddressQueryFacade.queryByOrderId(saleOrderVo.getTenantId(), orderId);
        saleOrderVo.setOrderAfterSaleAddressVO(OrderAddressConverter.INSTANCE.convertToVO(orderAddressResp));
        return CommonResult.ok(saleOrderVo);
    }

    @Override
    public CommonResult<List<OrderAfterSaleProductVO>> getOrderAfterSaleCommodity(Long orderId) {
        AssertParam.notNull(orderId, ResultDTOEnum.PARAMETER_MISSING.getCode(), "订单编号不能为空");
        List<OrderAfterSaleResp> afterSales = orderAfterSaleQueryFacade.queryByOrderId(orderId, null);

        AssertBiz.notNull(afterSales, ResultDTOEnum.PARAMETER_MISSING.getCode(), "售后订单不存在");

        List<Long> orderItemIds = afterSales.stream().map(OrderAfterSaleResp::getOrderItemId).distinct().collect(Collectors.toList());

        List<OrderItemSnapshotResp> orderItemSnapshotResps = orderItemSnapshotQueryFacade.queryByOrderItemIds(orderItemIds);
        Map<Long, OrderItemSnapshotResp> orderItemSnapshotDTOMap = orderItemSnapshotResps.stream().collect(Collectors.toMap(OrderItemSnapshotResp::getOrderItemId, Function.identity(), (v1, v2) -> v1));
        Map<Long, CategoryResp> skuIdCategoryRespMap = queryItemCategoryType(orderItemSnapshotResps);
        log.info("售后详情,orderId:{},skuIdCategoryRespMap:{}", orderId, JSON.toJSONString(skuIdCategoryRespMap));

        List<OrderItemResp> orderItemResps = orderItemQueryFacade.queryByIds(orderItemIds);
        Map<Long, OrderItemResp> orderItemDTOMap = orderItemResps.stream().collect(Collectors.toMap(OrderItemResp::getId, Function.identity(), (v1, v2) -> v1));

        List<OrderAfterSaleBizDTO> orderAfterSaleBizDTOS = afterSales.stream().map(dto -> {
            OrderAfterSaleBizDTO sale = OrderAfterSaleConverter.INSTANCE.resp2BizDto(dto);

            Long orderItemId = dto.getOrderItemId();
            sale.setSkuId(orderItemSnapshotDTOMap.get(orderItemId).getSkuId());
            sale.setAfterSaleUnit(orderItemSnapshotDTOMap.get(orderItemId).getAfterSaleUnit());
            sale.setSpecificationUnit(orderItemSnapshotDTOMap.get(orderItemId).getSpecificationUnit());
            Integer goodsType = orderItemSnapshotDTOMap.get(orderItemId).getGoodsType();
            sale.setStatus(getFrontShowStatus(sale.getTenantId(), sale.getStatus(), sale.getWarehouseType(), goodsType));
            sale.setAfterSaleUnit(OrderAfterSaleServiceTypeEnum.verifyIsReceivedRefund(sale.getServiceType(), sale.getAfterSaleType()) ? sale.getAfterSaleUnit() : sale.getSpecificationUnit());
            // 获取下单账号信息
            MerchantStoreAccountResultResp merchantStoreAccountVO = merchantStoreAccountService.getAccountInfo(sale.getAccountId());
            StringBuffer accountName = new StringBuffer(merchantStoreAccountVO.getAccountName())
                    .append("(").append(merchantStoreAccountVO.getPhone()).append(")");
            sale.setAccountName(accountName.toString());

            return sale;
        }).collect(Collectors.toList());

        List<OrderAfterSaleProductVO> resultVo = new ArrayList<>();

        Map<Long, List<OrderAfterSaleBizDTO>> orderAfterSaleMap = orderAfterSaleBizDTOS.stream().collect(Collectors.groupingBy(OrderAfterSaleBizDTO::getOrderItemId));

        for (Map.Entry<Long, List<OrderAfterSaleBizDTO>> entry : orderAfterSaleMap.entrySet()) {
            Long orderItemId = entry.getKey();
            List<OrderAfterSaleBizDTO> bizDTOList = entry.getValue();
            OrderItemSnapshotResp dto = orderItemSnapshotDTOMap.get(orderItemId);
            if (dto == null) {
                continue;
            }
            OrderAfterSaleProductVO afterSaleProductVO = new OrderAfterSaleProductVO();
            afterSaleProductVO.setMainPicture(dto.getMainPicture());
            afterSaleProductVO.setOrderNo(orderItemDTOMap.get(orderItemId).getOrderId() + "");
            afterSaleProductVO.setSkuId(orderItemDTOMap.get(orderItemId).getItemId());
            Integer categoryType = getCategoryType(orderItemSnapshotDTOMap, skuIdCategoryRespMap, orderItemId);
            afterSaleProductVO.setCategoryType(categoryType);
            afterSaleProductVO.setTitle(dto.getTitle());
            afterSaleProductVO.setWarehouseType(dto.getWarehouseType());
            afterSaleProductVO.setDeliveryType(dto.getDeliveryType());
            afterSaleProductVO.setAfterSaleOrders(bizDTOList.stream().sorted(Comparator.comparing(OrderAfterSaleBizDTO::getCreateTime).reversed()).collect(Collectors.toList()));
            resultVo.add(afterSaleProductVO);
        }

        return CommonResult.ok(resultVo);
    }

    private Integer getCategoryType(Map<Long, OrderItemSnapshotResp> orderItemSnapshotDTOMap, Map<Long, CategoryResp> skuIdCategoryRespMap, Long orderItemId) {
        Integer categoryType = OrderProductCategoryTypeEnum.DEFAULT.getType();
        Long skuId = Optional.ofNullable(orderItemSnapshotDTOMap.get(orderItemId)).map(OrderItemSnapshotResp::getSkuId).orElse(null);
        if (Objects.isNull(skuId)) {
            return categoryType;
        }

        // 填充订单商品项所属货品分类类型
        CategoryResp categoryResp = skuIdCategoryRespMap.get(skuId);
        if (Objects.nonNull(categoryResp) && Objects.nonNull(categoryResp.getType())) {
            categoryType = OrderProductCategoryTypeEnum.STANDARD.getType();
            if (CategoryTypeEnum.FRUIT.getValue().equals(categoryResp.getType())) {
                categoryType = OrderProductCategoryTypeEnum.FRUITS.getType();
            }
        }
        return categoryType;
    }

    /**
     * 查询订单商品所属货品分类
     * @param orderItemSnapshotDTOS
     * @return
     */
    private Map<Long, CategoryResp> queryItemCategoryType(List<OrderItemSnapshotResp> orderItemSnapshotDTOS) {
        if (CollectionUtil.isEmpty(orderItemSnapshotDTOS)) {
            return Collections.emptyMap();
        }
        List<Long> skuIds = orderItemSnapshotDTOS.stream().filter(snapshot ->
                Objects.nonNull(snapshot.getSkuId()) && snapshot.getSkuId() > NumberConstants.ZERO)
                .map(OrderItemSnapshotResp::getSkuId).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(skuIds)) {
            return Collections.emptyMap();
        }
        List<ProductSkuDetailResp> productSkuDetailResps = productQueryFacade.selectProductSkuDetailById(skuIds);
        List<Long> categoryIds = productSkuDetailResps.stream().filter(resp -> Objects.nonNull(resp.getCategoryId()))
                .map(ProductSkuDetailResp::getCategoryId).distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(categoryIds)) {
            return Collections.emptyMap();
        }
        List<CategoryResp> categoryResps = categoryServiceFacade.selectCategoryDetail(categoryIds);
        Map<Long, CategoryResp> categoryRespMap = categoryResps.stream().collect(Collectors.toMap(CategoryResp::getId, Function.identity(), (v1, v2) -> v1));

        Map<Long, CategoryResp> skuCategoryMap = productSkuDetailResps.stream().filter(sku ->
                Objects.nonNull(categoryRespMap.get(sku.getCategoryId())))
                .collect(Collectors.toMap(sku -> sku.getSkuId(), sku ->
                        categoryRespMap.get(sku.getCategoryId()), (v1, v2) -> v1));
        return skuCategoryMap;
    }


    @Override
    public CommonResult getOrderCommodity(Long orderId) {
        AssertParam.notNull(orderId, ResultDTOEnum.PARAMETER_MISSING.getCode(), "订单编号不能为空");
        OrderInfoVO orderInfoVo = new OrderInfoVO();
//        OrderDTO order = getOrder(orderId);
        OrderResp orderResp = orderQueryFacade.queryById(orderId);
        OrderVO orderVO = OrderMapper.INSTANCE.respToVO(orderResp);
        //查询店铺信息
        MerchantStoreResultResp merchantStoreInfo = merchantStoreService.getMerchantStoreInfo(orderVO.getStoreId());
        if (merchantStoreInfo != null) {
            orderVO.setStoreName(merchantStoreInfo.getStoreName());
            orderVO.setStoreType(merchantStoreInfo.getType());
        }

        // 获取订单项信息
//        List<OrderItemAndSnapshotDTO> orderItemAndSnapshotDTOList = getOrderItemAndSnapshotDTOS(orderId);
        List<OrderItemAndSnapshotResp> orderItemAndSnapshotResps = orderItemQueryFacade.queryByOrderId(orderId);
        List<OrderItemVO> orderItemVOS = orderItemAndSnapshotResps.stream().map(OrderItemMapper.INSTANCE::respToVo).collect(Collectors.toList());
        BigDecimal productTotalPrice = BigDecimal.ZERO;
        for (OrderItemVO orderItemVO : orderItemVOS) {
            productTotalPrice = NumberUtil.add(productTotalPrice, orderItemVO.getTotalPrice());
        }
        orderInfoVo.setProductTotalPrice(productTotalPrice);
        orderInfoVo.setDeliveryFee(orderVO.getDeliveryFee());
        orderInfoVo.setTotalPrice(orderVO.getTotalPrice());
        orderInfoVo.setPayablePrice(orderVO.getPayablePrice());

        orderInfoVo.setOrderItemVOS(orderItemVOS);
        return CommonResult.ok(orderInfoVo);
    }

    @Override
    public BigDecimal calculateRefundPrice(Long orderItemId, Integer amount) {
        OrderAfterSaleCalRefundPriceReq req = new OrderAfterSaleCalRefundPriceReq();
        req.setOrderItemId(orderItemId);
        req.setQuantity(amount);
        return orderAfterSaleQueryFacade.calculateRefundPrice(req);
    }

    @Override
    public Boolean serviceProviderReviewSubmissions(OrderAfterSaleAuditDTO orderAfterSaleAuditDTO) {
        OrderAfterSaleAuditReq orderAfterSaleAuditReq = buildAuditReq(orderAfterSaleAuditDTO);
        return orderAfterSaleCommandFacade.serviceProviderReviewSubmissions(orderAfterSaleAuditReq);
    }

    private OrderAfterSaleAuditReq buildAuditReq(OrderAfterSaleAuditDTO orderAfterSaleAuditDTO) {
        OrderAfterSaleAuditReq orderAfterSaleAuditReq = new OrderAfterSaleAuditReq();
        orderAfterSaleAuditReq.setAfterSaleOrderNo(orderAfterSaleAuditDTO.getAfterSaleOrderNo());
        orderAfterSaleAuditReq.setHandleRemark(orderAfterSaleAuditDTO.getHandleRemark());
        orderAfterSaleAuditReq.setAuditStatus(orderAfterSaleAuditDTO.getAuditStatus());
        orderAfterSaleAuditReq.setResponsibilityType(orderAfterSaleAuditDTO.getResponsibilityType());
        orderAfterSaleAuditReq.setTotalPrice(orderAfterSaleAuditDTO.getTotalPrice());
        return orderAfterSaleAuditReq;
    }
}
