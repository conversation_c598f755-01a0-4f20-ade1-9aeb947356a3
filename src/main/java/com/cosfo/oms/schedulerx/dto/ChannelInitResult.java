package com.cosfo.oms.schedulerx.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 渠道初始化任务结果
 * 
 * <AUTHOR>
 * @date 2025-08-18
 */
@Data
public class ChannelInitResult {
    
    /**
     * 总处理数量
     */
    private int totalCount = 0;
    
    /**
     * 成功数量
     */
    private int successCount = 0;
    
    /**
     * 跳过数量（已初始化）
     */
    private int skipCount = 0;
    
    /**
     * 失败数量
     */
    private int failCount = 0;
    
    /**
     * 错误信息列表
     */
    private List<String> errors = new ArrayList<>();
    
    /**
     * 添加成功记录
     */
    public void addSuccess() {
        this.successCount++;
        this.totalCount++;
    }
    
    /**
     * 添加跳过记录
     */
    public void addSkip() {
        this.skipCount++;
        this.totalCount++;
    }
    
    /**
     * 添加失败记录
     */
    public void addFail(String error) {
        this.failCount++;
        this.totalCount++;
        this.errors.add(error);
    }
    
    /**
     * 获取结果摘要
     */
    public String getSummary() {
        return String.format("处理完成：总数%d，成功%d，跳过%d，失败%d", 
            totalCount, successCount, skipCount, failCount);
    }
    
    /**
     * 是否有错误
     */
    public boolean hasErrors() {
        return failCount > 0;
    }
}
