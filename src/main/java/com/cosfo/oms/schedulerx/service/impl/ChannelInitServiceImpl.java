package com.cosfo.oms.schedulerx.service.impl;

import com.alibaba.fastjson.JSON;
import com.cosfo.oms.common.context.IndirectChannelTypeEnum;
import com.cosfo.oms.common.context.PaymentMethodSwitchEnum;
import com.cosfo.oms.facade.PaymentChannelFacade;
import com.cosfo.oms.schedulerx.dto.ChannelInitResult;
import com.cosfo.oms.schedulerx.dto.ChannelInitTaskParam;
import com.cosfo.oms.schedulerx.service.ChannelInitService;
import com.cosfo.oms.tenant.dao.TenantAuthConnectionDao;
import com.cosfo.oms.tenant.model.dto.TenantAuthConnQueryDTO;
import com.cosfo.oms.tenant.model.dto.TenantAuthServiceDTO;
import com.cosfo.oms.tenant.model.po.TenantAuthConnection;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 渠道初始化服务实现
 *
 * <AUTHOR>
 * @date 2025-08-18
 */
@Service
@Slf4j
public class ChannelInitServiceImpl implements ChannelInitService {

    @Resource
    private TenantAuthConnectionDao tenantAuthConnectionDao;

    @Resource
    private PaymentChannelFacade paymentChannelFacade;

    @Override
    public ChannelInitResult executeChannelInit(ChannelInitTaskParam param) {
        log.info("开始执行渠道初始化任务，参数: {}", JSON.toJSONString(param));

        ChannelInitResult result = new ChannelInitResult();

        try {
            // 解析租户ID列表
            List<Long> tenantIdList = parseTenantIds(param.getTenantIds());

            // 分页查询历史配置
            int offset = 0;
            int batchSize = param.getBatchSize();
            int batchNumber = 1;

            do {
                List<TenantAuthConnection> batch = queryHistoryConfigs(tenantIdList, offset, batchSize);

                if (CollectionUtils.isEmpty(batch)) {
                    log.info("没有更多支付配置数据需要处理，结束任务");
                    break;
                }

                log.info("处理第{}批支付配置数据，数量: {}", batchNumber, batch.size());

                // 处理当前批次
                processBatch(batch, result);

                offset += batchSize;
                batchNumber++;

                // 安全检查：如果返回的数据少于批大小，说明已经是最后一批
                if (batch.size() < batchSize) {
                    log.info("当前批次数据量({})小于批大小({})，已处理完所有数据", batch.size(), batchSize);
                    break;
                }

            } while (true);

            log.info("渠道初始化任务执行完成，{}", result.getSummary());

        } catch (Exception e) {
            log.error("渠道初始化任务执行异常", e);
            result.addFail("任务执行异常: " + e.getMessage());
        }

        return result;
    }

    /**
     * 解析租户ID列表
     */
    private List<Long> parseTenantIds(String tenantIds) {
        List<Long> tenantIdList = new ArrayList<>();

        if (StringUtils.hasText(tenantIds)) {
            String[] idArray = tenantIds.split(",");
            for (String idStr : idArray) {
                try {
                    Long tenantId = Long.parseLong(idStr.trim());
                    tenantIdList.add(tenantId);
                } catch (NumberFormatException e) {
                    log.warn("无效的租户ID: {}", idStr);
                }
            }
            log.info("解析到{}个指定租户ID: {}", tenantIdList.size(), tenantIdList);
        } else {
            log.info("未指定租户ID，将处理所有租户的支付配置");
        }

        return tenantIdList.isEmpty() ? null : tenantIdList;
    }

    /**
     * 查询历史支付配置
     */
    private List<TenantAuthConnection> queryHistoryConfigs(List<Long> tenantIdList, int offset, int batchSize) {
        // 如果指定了租户ID列表，需要分别查询每个租户
        if (tenantIdList != null && !tenantIdList.isEmpty()) {
            List<TenantAuthConnection> allConfigs = new ArrayList<>();
            for (Long tenantId : tenantIdList) {
                TenantAuthConnQueryDTO queryDTO = TenantAuthConnQueryDTO.builder()
                        .tenantId(tenantId)
                        .offset(0) // 指定租户时不分页，直接获取该租户的所有配置
                        .limit(null)
                        .build();
                List<TenantAuthConnection> configs = tenantAuthConnectionDao.listByParam(queryDTO);
                allConfigs.addAll(configs);
            }

            // 手动分页
            int start = offset;
            int end = Math.min(start + batchSize, allConfigs.size());
            if (start >= allConfigs.size()) {
                return new ArrayList<>();
            }
            return allConfigs.subList(start, end);
        } else {
            // 处理所有租户，使用数据库分页
            TenantAuthConnQueryDTO queryDTO = TenantAuthConnQueryDTO.builder()
                    .tenantId(null)
                    .offset(offset)
                    .limit(batchSize)
                    .build();
            return tenantAuthConnectionDao.listByParam(queryDTO);
        }
    }

    /**
     * 处理批次数据
     */
    private void processBatch(List<TenantAuthConnection> batch, ChannelInitResult result) {
        for (TenantAuthConnection connection : batch) {
            try {
                processConnection(connection, result);
            } catch (Exception e) {
                log.error("处理租户支付配置失败，configId: {}, tenantId: {}",
                    connection.getId(), connection.getTenantId(), e);
                result.addFail(String.format("租户%d支付配置处理失败: %s", connection.getTenantId(), e.getMessage()));
            }
        }
    }

    /**
     * 处理单个支付配置
     */
    private void processConnection(TenantAuthConnection connection, ChannelInitResult result) {
        log.info("处理租户支付配置，configId: {}, tenantId: {}", connection.getId(), connection.getTenantId());

        boolean hasValidConfig = false;

        // 检查并处理微信直连配置
        if (hasWechatDirectConfig(connection)) {
            processWechatDirectChannel(connection, result);
            hasValidConfig = true;
        }

        // 检查并处理汇付间连配置
        if (hasHuifuIndirectConfig(connection)) {
            processHuifuIndirectChannel(connection, result);
            hasValidConfig = true;
        }

        // 如果没有任何有效配置
        if (!hasValidConfig) {
            log.debug("租户{}没有有效的支付配置，跳过", connection.getTenantId());
            result.addSkip();
        }
    }

    /**
     * 检查是否有微信直连配置
     */
    private boolean hasWechatDirectConfig(TenantAuthConnection connection) {
        return PaymentMethodSwitchEnum.ENABLED.getCode().equals(connection.getWechatDirectSwitch())
                && StringUtils.hasText(connection.getPayMchid())
                && StringUtils.hasText(connection.getPaySecret())
                && StringUtils.hasText(connection.getPayCertPath());
    }

    /**
     * 检查是否有汇付间连配置
     */
    private boolean hasHuifuIndirectConfig(TenantAuthConnection connection) {
        // 检查任一间连开关是否开启
        boolean hasIndirectSwitch = PaymentMethodSwitchEnum.ENABLED.getCode().equals(connection.getWechatIndirectSwitch())
                || PaymentMethodSwitchEnum.ENABLED.getCode().equals(connection.getWechatIndirectPluginSwitch())
                || PaymentMethodSwitchEnum.ENABLED.getCode().equals(connection.getH5WechatIndirectSwitch());

        // 检查汇付相关参数
        boolean hasHuifuParams = StringUtils.hasText(connection.getHuifuId())
                && StringUtils.hasText(connection.getHuifuPublicKey())
                && StringUtils.hasText(connection.getPublicKey())
                && StringUtils.hasText(connection.getSecretKey());

        return hasIndirectSwitch && hasHuifuParams;
    }

    /**
     * 处理微信直连渠道
     */
    private void processWechatDirectChannel(TenantAuthConnection connection, ChannelInitResult result) {
        try {
            // 构建微信直连配置DTO
            TenantAuthServiceDTO dto = buildWechatDirectDto(connection);

            // 调用PaymentChannelFacade保存配置（RPC接口已做幂等）
            List<Long> channelIds = paymentChannelFacade.saveMultiChannelConfigs(dto);

            if (CollectionUtils.isEmpty(channelIds)) {
                log.warn("租户{}微信直连渠道初始化未返回渠道ID", connection.getTenantId());
                result.addFail(String.format("租户%d微信直连初始化未返回渠道ID", connection.getTenantId()));
                return;
            }

            result.addSuccess();
            log.info("租户{}微信直连渠道初始化成功，channelIds: {}", connection.getTenantId(), channelIds);

        } catch (Exception e) {
            log.error("租户{}微信直连渠道初始化失败", connection.getTenantId(), e);
            result.addFail(String.format("租户%d微信直连初始化失败: %s", connection.getTenantId(), e.getMessage()));
        }
    }

    /**
     * 处理汇付间连渠道
     */
    private void processHuifuIndirectChannel(TenantAuthConnection connection, ChannelInitResult result) {
        try {
            // 构建汇付间连配置DTO
            TenantAuthServiceDTO dto = buildHuifuIndirectDto(connection);

            // 调用PaymentChannelFacade保存配置（RPC接口已做幂等）
            List<Long> channelIds = paymentChannelFacade.saveMultiChannelConfigs(dto);

            if (CollectionUtils.isEmpty(channelIds)) {
                log.warn("租户{}汇付间连渠道初始化未返回渠道ID", connection.getTenantId());
                result.addFail(String.format("租户%d汇付间连初始化未返回渠道ID", connection.getTenantId()));
                return;
            }

            result.addSuccess();
            log.info("租户{}汇付间连渠道初始化成功，channelIds: {}", connection.getTenantId(), channelIds);

        } catch (Exception e) {
            log.error("租户{}汇付间连渠道初始化失败", connection.getTenantId(), e);
            result.addFail(String.format("租户%d汇付间连初始化失败: %s", connection.getTenantId(), e.getMessage()));
        }
    }

    /**
     * 构建微信直连DTO
     */
    private TenantAuthServiceDTO buildWechatDirectDto(TenantAuthConnection connection) {
        TenantAuthServiceDTO dto = new TenantAuthServiceDTO();
        dto.setTenantId(connection.getTenantId());

        // 微信直连配置
        dto.setWechatDirectSwitch(PaymentMethodSwitchEnum.ENABLED.getCode());
        dto.setPayMchid(connection.getPayMchid());
        dto.setPaySecret(connection.getPaySecret());
        dto.setPayCertPath(connection.getPayCertPath());

        // 关闭其他渠道
        dto.setWechatIndirectSwitch(PaymentMethodSwitchEnum.DISABLED.getCode());
        dto.setWechatIndirectPluginSwitch(PaymentMethodSwitchEnum.DISABLED.getCode());
        dto.setH5WechatIndirectSwitch(PaymentMethodSwitchEnum.DISABLED.getCode());
        dto.setAliIndirectSwitch(PaymentMethodSwitchEnum.DISABLED.getCode());

        // 设置支付场景开关（根据原有配置）
        dto.setAppletWechatPaySwitch(connection.getAppletWechatPaySwitch());

        return dto;
    }

    /**
     * 构建汇付间连DTO
     */
    private TenantAuthServiceDTO buildHuifuIndirectDto(TenantAuthConnection connection) {
        TenantAuthServiceDTO dto = new TenantAuthServiceDTO();
        dto.setTenantId(connection.getTenantId());

        // 关闭微信直连
        dto.setWechatDirectSwitch(PaymentMethodSwitchEnum.DISABLED.getCode());

        // 汇付间连配置
        dto.setIndirectOnlineChannel(IndirectChannelTypeEnum.HUI_FU.getCode()); // 汇付渠道
        dto.setHuifuId(connection.getHuifuId());
        dto.setSecretKey(connection.getSecretKey());
        dto.setPublicKey(connection.getPublicKey());
        dto.setHuifuPublicKey(connection.getHuifuPublicKey());

        // 设置间连开关（根据原有配置）
        dto.setWechatIndirectSwitch(connection.getWechatIndirectSwitch());
        dto.setWechatIndirectPluginSwitch(connection.getWechatIndirectPluginSwitch());
        dto.setH5WechatIndirectSwitch(connection.getH5WechatIndirectSwitch());
        dto.setAliIndirectSwitch(PaymentMethodSwitchEnum.DISABLED.getCode()); // 只处理汇付，不处理支付宝

        // 设置支付场景开关
        dto.setAppletWechatPaySwitch(connection.getAppletWechatPaySwitch());
        dto.setOaAppId(connection.getOaAppId());
        dto.setOaAppSecret(connection.getOaAppSecret());

        return dto;
    }
}
