package com.cosfo.oms.schedulerx.service.impl;

import com.alibaba.fastjson.JSON;
import com.cosfo.oms.common.context.PaymentMethodSwitchEnum;
import com.cosfo.oms.facade.PaymentChannelFacade;
import com.cosfo.oms.schedulerx.dto.ChannelInitResult;
import com.cosfo.oms.schedulerx.dto.ChannelInitTaskParam;
import com.cosfo.oms.schedulerx.service.ChannelInitService;
import com.cosfo.oms.tenant.dao.TenantAuthConnectionDao;
import com.cosfo.oms.tenant.mapper.ChannelInitRecordMapper;
import com.cosfo.oms.tenant.model.dto.TenantAuthConnQueryDTO;
import com.cosfo.oms.tenant.model.dto.TenantAuthServiceDTO;
import com.cosfo.oms.tenant.model.po.ChannelInitRecord;
import com.cosfo.oms.tenant.model.po.TenantAuthConnection;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 渠道初始化服务实现
 *
 * <AUTHOR>
 * @date 2025-08-18
 */
@Service
@Slf4j
public class ChannelInitServiceImpl implements ChannelInitService {

    @Resource
    private TenantAuthConnectionDao tenantAuthConnectionDao;

    @Resource
    private ChannelInitRecordMapper channelInitRecordMapper;

    @Resource
    private PaymentChannelFacade paymentChannelFacade;

    @Override
    public ChannelInitResult executeChannelInit(ChannelInitTaskParam param) {
        log.info("开始执行渠道初始化任务，参数: {}", JSON.toJSONString(param));

        ChannelInitResult result = new ChannelInitResult();

        try {
            // 分页查询历史配置
            int offset = 0;
            int batchSize = param.getBatchSize();

            while (true) {
                List<TenantAuthConnection> batch = queryHistoryConfigs(param.getTenantId(), offset, batchSize);

                if (CollectionUtils.isEmpty(batch)) {
                    log.info("没有更多数据需要处理，结束任务");
                    break;
                }

                log.info("处理第{}批数据，数量: {}", (offset / batchSize) + 1, batch.size());

                // 处理当前批次
                processBatch(batch, param, result);

                offset += batchSize;
            }

            log.info("渠道初始化任务执行完成，{}", result.getSummary());

        } catch (Exception e) {
            log.error("渠道初始化任务执行异常", e);
            result.addFail("任务执行异常: " + e.getMessage());
        }

        return result;
    }

    /**
     * 查询历史配置
     */
    private List<TenantAuthConnection> queryHistoryConfigs(Long tenantId, int offset, int batchSize) {
        TenantAuthConnQueryDTO queryDTO = TenantAuthConnQueryDTO.builder()
                .tenantId(tenantId)
                .offset(offset)
                .limit(batchSize)
                .build();

        return tenantAuthConnectionDao.listByParam(queryDTO);
    }

    /**
     * 处理批次数据
     */
    private void processBatch(List<TenantAuthConnection> batch, ChannelInitTaskParam param, ChannelInitResult result) {
        for (TenantAuthConnection connection : batch) {
            try {
                processConnection(connection, param, result);
            } catch (Exception e) {
                log.error("处理租户认证连接失败，connectionId: {}, tenantId: {}",
                    connection.getId(), connection.getTenantId(), e);
                result.addFail(String.format("租户%d处理失败: %s", connection.getTenantId(), e.getMessage()));
            }
        }
    }

    /**
     * 处理单个连接
     */
    private void processConnection(TenantAuthConnection connection, ChannelInitTaskParam param, ChannelInitResult result) {
        log.info("处理租户认证连接，connectionId: {}, tenantId: {}", connection.getId(), connection.getTenantId());

        // 检查微信直连配置
        if (hasWechatDirectConfig(connection)) {
            processWechatDirectChannel(connection, param, result);
        }

        // 检查汇付间连配置
        if (hasHuifuIndirectConfig(connection)) {
            processHuifuIndirectChannel(connection, param, result);
        }

        // 如果没有任何有效配置
        if (!hasWechatDirectConfig(connection) && !hasHuifuIndirectConfig(connection)) {
            log.debug("租户{}没有有效的支付配置，跳过", connection.getTenantId());
            result.addSkip();
        }
    }

    /**
     * 检查是否有微信直连配置
     */
    private boolean hasWechatDirectConfig(TenantAuthConnection connection) {
        return PaymentMethodSwitchEnum.ENABLED.getCode().equals(connection.getWechatDirectSwitch())
                && StringUtils.hasText(connection.getPayMchid())
                && StringUtils.hasText(connection.getPaySecret())
                && StringUtils.hasText(connection.getPayCertPath());
    }

    /**
     * 检查是否有汇付间连配置
     */
    private boolean hasHuifuIndirectConfig(TenantAuthConnection connection) {
        // 检查任一间连开关是否开启
        boolean hasIndirectSwitch = PaymentMethodSwitchEnum.ENABLED.getCode().equals(connection.getWechatIndirectSwitch())
                || PaymentMethodSwitchEnum.ENABLED.getCode().equals(connection.getWechatIndirectPluginSwitch())
                || PaymentMethodSwitchEnum.ENABLED.getCode().equals(connection.getH5WechatIndirectSwitch());

        // 检查汇付相关参数
        boolean hasHuifuParams = StringUtils.hasText(connection.getHuifuId())
                && StringUtils.hasText(connection.getHuifuPublicKey())
                && StringUtils.hasText(connection.getPublicKey())
                && StringUtils.hasText(connection.getSecretKey());

        return hasIndirectSwitch && hasHuifuParams;
    }

    /**
     * 处理微信直连渠道
     */
    private void processWechatDirectChannel(TenantAuthConnection connection, ChannelInitTaskParam param, ChannelInitResult result) {
        String channelType = ChannelInitRecord.ChannelType.WECHAT_DIRECT.getCode();

        // 检查是否已初始化
        if (!param.getForceReinit() && isAlreadyInitialized(connection, channelType)) {
            log.debug("租户{}微信直连渠道已初始化，跳过", connection.getTenantId());
            result.addSkip();
            return;
        }

        try {
            // 初始化渠道配置
            Long channelId = initializeWechatDirectChannel(connection);

            // 记录初始化成功
            recordInitSuccess(connection, channelType, channelId);
            result.addSuccess();

            log.info("租户{}微信直连渠道初始化成功，channelId: {}", connection.getTenantId(), channelId);

        } catch (Exception e) {
            log.error("租户{}微信直连渠道初始化失败", connection.getTenantId(), e);

            // 记录初始化失败
            recordInitFailure(connection, channelType, e.getMessage());
            result.addFail(String.format("租户%d微信直连初始化失败: %s", connection.getTenantId(), e.getMessage()));
        }
    }

    /**
     * 处理汇付间连渠道
     */
    private void processHuifuIndirectChannel(TenantAuthConnection connection, ChannelInitTaskParam param, ChannelInitResult result) {
        String channelType = ChannelInitRecord.ChannelType.HUIFU_INDIRECT.getCode();

        // 检查是否已初始化
        if (!param.getForceReinit() && isAlreadyInitialized(connection, channelType)) {
            log.debug("租户{}汇付间连渠道已初始化，跳过", connection.getTenantId());
            result.addSkip();
            return;
        }

        try {
            // 初始化渠道配置
            Long channelId = initializeHuifuIndirectChannel(connection);

            // 记录初始化成功
            recordInitSuccess(connection, channelType, channelId);
            result.addSuccess();

            log.info("租户{}汇付间连渠道初始化成功，channelId: {}", connection.getTenantId(), channelId);

        } catch (Exception e) {
            log.error("租户{}汇付间连渠道初始化失败", connection.getTenantId(), e);

            // 记录初始化失败
            recordInitFailure(connection, channelType, e.getMessage());
            result.addFail(String.format("租户%d汇付间连初始化失败: %s", connection.getTenantId(), e.getMessage()));
        }
    }

    /**
     * 检查是否已初始化
     */
    private boolean isAlreadyInitialized(TenantAuthConnection connection, String channelType) {
        ChannelInitRecord record = channelInitRecordMapper.selectByTenantAndChannelType(
                connection.getTenantId(), connection.getId(), channelType);

        return record != null && ChannelInitRecord.InitStatus.SUCCESS.getCode().equals(record.getInitStatus());
    }

    /**
     * 初始化微信直连渠道
     */
    private Long initializeWechatDirectChannel(TenantAuthConnection connection) {
        // 构建TenantAuthServiceDTO
        TenantAuthServiceDTO dto = buildWechatDirectDto(connection);

        // 调用PaymentChannelFacade保存配置
        List<Long> channelIds = paymentChannelFacade.saveMultiChannelConfigs(dto);

        if (CollectionUtils.isEmpty(channelIds)) {
            throw new RuntimeException("微信直连渠道初始化失败，未返回渠道ID");
        }

        return channelIds.get(0);
    }

    /**
     * 初始化汇付间连渠道
     */
    private Long initializeHuifuIndirectChannel(TenantAuthConnection connection) {
        // 构建TenantAuthServiceDTO
        TenantAuthServiceDTO dto = buildHuifuIndirectDto(connection);

        // 调用PaymentChannelFacade保存配置
        List<Long> channelIds = paymentChannelFacade.saveMultiChannelConfigs(dto);

        if (CollectionUtils.isEmpty(channelIds)) {
            throw new RuntimeException("汇付间连渠道初始化失败，未返回渠道ID");
        }

        return channelIds.get(0);
    }

    /**
     * 构建微信直连DTO
     */
    private TenantAuthServiceDTO buildWechatDirectDto(TenantAuthConnection connection) {
        TenantAuthServiceDTO dto = new TenantAuthServiceDTO();
        dto.setTenantId(connection.getTenantId());

        // 微信直连配置
        dto.setWechatDirectSwitch(PaymentMethodSwitchEnum.ENABLED.getCode());
        dto.setPayMchid(connection.getPayMchid());
        dto.setPaySecret(connection.getPaySecret());
        dto.setPayCertPath(connection.getPayCertPath());

        // 关闭其他渠道
        dto.setWechatIndirectSwitch(PaymentMethodSwitchEnum.DISABLED.getCode());
        dto.setWechatIndirectPluginSwitch(PaymentMethodSwitchEnum.DISABLED.getCode());
        dto.setH5WechatIndirectSwitch(PaymentMethodSwitchEnum.DISABLED.getCode());
        dto.setAliIndirectSwitch(PaymentMethodSwitchEnum.DISABLED.getCode());

        // 设置支付场景开关（根据原有配置）
        dto.setAppletWechatPaySwitch(connection.getAppletWechatPaySwitch());

        return dto;
    }

    /**
     * 构建汇付间连DTO
     */
    private TenantAuthServiceDTO buildHuifuIndirectDto(TenantAuthConnection connection) {
        TenantAuthServiceDTO dto = new TenantAuthServiceDTO();
        dto.setTenantId(connection.getTenantId());

        // 关闭微信直连
        dto.setWechatDirectSwitch(PaymentMethodSwitchEnum.DISABLED.getCode());

        // 汇付间连配置
        dto.setIndirectOnlineChannel(1); // 汇付渠道
        dto.setHuifuId(connection.getHuifuId());
        dto.setSecretKey(connection.getSecretKey());
        dto.setPublicKey(connection.getPublicKey());
        dto.setHuifuPublicKey(connection.getHuifuPublicKey());

        // 设置间连开关（根据原有配置）
        dto.setWechatIndirectSwitch(connection.getWechatIndirectSwitch());
        dto.setWechatIndirectPluginSwitch(connection.getWechatIndirectPluginSwitch());
        dto.setH5WechatIndirectSwitch(connection.getH5WechatIndirectSwitch());
        dto.setAliIndirectSwitch(PaymentMethodSwitchEnum.DISABLED.getCode()); // 只处理汇付，不处理支付宝

        // 设置支付场景开关
        dto.setAppletWechatPaySwitch(connection.getAppletWechatPaySwitch());
        dto.setOaAppId(connection.getOaAppId());
        dto.setOaAppSecret(connection.getOaAppSecret());

        return dto;
    }

    /**
     * 记录初始化成功
     */
    @Transactional(rollbackFor = Exception.class)
    private void recordInitSuccess(TenantAuthConnection connection, String channelType, Long channelId) {
        try {
            ChannelInitRecord record = new ChannelInitRecord();
            record.setTenantId(connection.getTenantId());
            record.setAuthConnectionId(connection.getId());
            record.setChannelType(channelType);
            record.setChannelId(channelId);
            record.setInitStatus(ChannelInitRecord.InitStatus.SUCCESS.getCode());
            record.setInitTime(new Date());
            record.setRetryCount(0);

            channelInitRecordMapper.insert(record);

        } catch (Exception e) {
            log.error("记录初始化成功状态失败，租户ID: {}, 渠道类型: {}",
                connection.getTenantId(), channelType, e);
            throw new RuntimeException("本地标记失败告警: " + e.getMessage());
        }
    }

    /**
     * 记录初始化失败
     */
    @Transactional(rollbackFor = Exception.class)
    private void recordInitFailure(TenantAuthConnection connection, String channelType, String errorMessage) {
        try {
            ChannelInitRecord record = new ChannelInitRecord();
            record.setTenantId(connection.getTenantId());
            record.setAuthConnectionId(connection.getId());
            record.setChannelType(channelType);
            record.setInitStatus(ChannelInitRecord.InitStatus.FAILED.getCode());
            record.setInitTime(new Date());
            record.setErrorMessage(errorMessage);
            record.setRetryCount(0);

            channelInitRecordMapper.insert(record);

        } catch (Exception e) {
            log.error("记录初始化失败状态失败，租户ID: {}, 渠道类型: {}",
                connection.getTenantId(), channelType, e);
            throw new RuntimeException("本地标记失败告警: " + e.getMessage());
        }
    }
}
