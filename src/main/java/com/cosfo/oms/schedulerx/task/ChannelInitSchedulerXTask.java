package com.cosfo.oms.schedulerx.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.oms.schedulerx.dto.ChannelInitResult;
import com.cosfo.oms.schedulerx.dto.ChannelInitTaskParam;
import com.cosfo.oms.schedulerx.service.ChannelInitService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * 渠道初始化SchedulerX任务
 * 
 * 任务参数示例：
 * 1. 处理全部租户：{}
 * 2. 处理指定租户：{"tenantId": 12345}
 * 3. 自定义批处理大小：{"tenantId": 12345, "batchSize": 100}
 * 4. 强制重新初始化：{"tenantId": 12345, "forceReinit": true}
 * 
 * <AUTHOR>
 * @date 2025-08-18
 */
@Component
@Slf4j
public class ChannelInitSchedulerXTask implements JavaProcessor {
    
    @Resource
    private ChannelInitService channelInitService;
    
    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String jobParameters = context.getJobParameters();
        long jobInstanceId = context.getJobInstanceId();
        
        log.info("开始执行渠道初始化定时任务，任务实例ID: {}, 参数: {}", jobInstanceId, jobParameters);
        
        try {
            // 解析任务参数
            ChannelInitTaskParam param = parseJobParameters(jobParameters);
            log.info("解析任务参数成功: {}", JSON.toJSONString(param));
            
            // 执行渠道初始化
            ChannelInitResult result = channelInitService.executeChannelInit(param);
            
            // 构建结果消息
            String resultMessage = buildResultMessage(result, param);
            
            // 判断任务是否成功
            boolean success = !result.hasErrors();
            
            if (success) {
                log.info("渠道初始化定时任务执行成功，任务实例ID: {}, {}", jobInstanceId, result.getSummary());
            } else {
                log.error("渠道初始化定时任务执行有错误，任务实例ID: {}, {}, 错误详情: {}", 
                    jobInstanceId, result.getSummary(), result.getErrors());
            }
            
            return new ProcessResult(success, resultMessage);
            
        } catch (Exception e) {
            log.error("渠道初始化定时任务执行异常，任务实例ID: {}", jobInstanceId, e);
            return new ProcessResult(false, "任务执行异常: " + e.getMessage());
        }
    }
    
    /**
     * 解析任务参数
     */
    private ChannelInitTaskParam parseJobParameters(String jobParameters) {
        ChannelInitTaskParam param = new ChannelInitTaskParam();
        
        if (StringUtils.hasText(jobParameters)) {
            try {
                param = JSON.parseObject(jobParameters, ChannelInitTaskParam.class);
            } catch (Exception e) {
                log.warn("解析任务参数失败，使用默认参数，原参数: {}", jobParameters, e);
            }
        }
        
        // 设置默认值
        if (param.getBatchSize() == null || param.getBatchSize() <= 0) {
            param.setBatchSize(50);
        }
        if (param.getForceReinit() == null) {
            param.setForceReinit(false);
        }
        
        return param;
    }
    
    /**
     * 构建结果消息
     */
    private String buildResultMessage(ChannelInitResult result, ChannelInitTaskParam param) {
        StringBuilder message = new StringBuilder();
        
        // 基本信息
        message.append("渠道初始化任务执行完成");
        if (param.getTenantId() != null) {
            message.append("，指定租户: ").append(param.getTenantId());
        }
        message.append("，").append(result.getSummary());
        
        // 错误信息
        if (result.hasErrors()) {
            message.append("，错误详情: ");
            for (int i = 0; i < Math.min(result.getErrors().size(), 5); i++) {
                if (i > 0) {
                    message.append("; ");
                }
                message.append(result.getErrors().get(i));
            }
            if (result.getErrors().size() > 5) {
                message.append("...(共").append(result.getErrors().size()).append("个错误)");
            }
        }
        
        return message.toString();
    }
}
