package com.cosfo.oms.tenant.controller;

import com.cosfo.oms.common.result.ResultDTO;
import com.cosfo.oms.common.result.ResultDTOEnum;
import com.cosfo.oms.controller.BaseController;
import com.cosfo.oms.model.vo.TenantDetailVO;
import com.cosfo.oms.tenant.convert.TenantMainInfoConvert;
import com.cosfo.oms.tenant.model.dto.*;
import com.cosfo.oms.tenant.model.vo.ProfitSharingRoleInfoVO;
import com.cosfo.oms.tenant.model.vo.SupplierTenantVO;
import com.cosfo.oms.tenant.model.vo.TenantVO;
import com.cosfo.oms.tenant.service.TenantService;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 描述: 租户控制类
 *
 * @author: <EMAIL>
 * @创建时间: 2022/6/10
 */
@RestController
@RequestMapping(value = {"/tenant", "/cosfo-oms/tenant"})
public class TenantController extends BaseController {
    @Resource
    private TenantService tenantService;

    @RequestMapping(value = "get/mall/info", method = RequestMethod.GET)
    public ResultDTO getMallInfo(String tenantName) {
        return tenantService.queryMallInfo(tenantName);
    }


    /**
     * 根据名称+类型查询租户列表
     * @param tenantName
     * @param type
     * @return
     */
    @PostMapping("query/tenant/info")
    public ResultDTO<List<TenantVO>> queryTenantInfo(@RequestBody TenantQueryDTO tenantQueryDTO) {
        return tenantService.queryTenantInfo(tenantQueryDTO);
    }


    /**
     * 查询商城列表
     *
     * @param tenantQueryDTO 商城列表查询信息
     * @return ResultDTO<PageInfo < TenantVO>>
     */
    @PostMapping("/query/list")
    public ResultDTO<PageInfo<TenantVO>> listAll(@RequestBody TenantQueryDTO tenantQueryDTO) {
        return tenantService.listAll(tenantQueryDTO);
    }

    /**
     * 查询商城详情
     *
     * @param tenantId 租户Id
     * @return
     */
    @PostMapping("/query/detail")
    public ResultDTO<TenantDetailVO> queryDetail(@RequestBody TenantQueryDTO tenantQueryDTO) {
        Long tenantId = tenantQueryDTO.getTenantId();
        if(tenantId == null) {
            return ResultDTO.fail(ResultDTOEnum.PARAMETER_MISSING.getCode(), "租户id为空");
        }
        return ResultDTO.success(tenantService.queryDetail(tenantId));
    }

    /**
     * 新增品牌方工商信息
     *
     * @param companyDTO 工商信息
     * @return
     */
    @PostMapping("/upsert/add-company-info")
    public ResultDTO insetTenantInfo(@RequestBody @Validated({ValidGroup.Insert.class}) TenantCompanyDTO companyDTO) {
        tenantService.insetTenantInfo(companyDTO);
        return ResultDTO.success();
    }

    /**
     * 更新品牌方工商信息
     *
     * @param companyDTO 工商信息
     * @return
     */
    @PostMapping("/upsert/edit-company-info")
    public ResultDTO updateTenantInfo(@RequestBody @Validated({ValidGroup.Update.class}) TenantCompanyDTO companyDTO) {
        tenantService.updateTenantInfo(companyDTO);
        return ResultDTO.success();
    }



    /**
     * 更新客服入口开关
     *
     * @param tenantConsultationEntranceDTO 工商信息
     * @return
     */
    @PostMapping("/upsert/edit-consultation-entrance")
    public ResultDTO updateTenantInfo(@RequestBody @Validated({ValidGroup.Update.class}) TenantConsultationEntranceDTO tenantConsultationEntranceDTO) {
        tenantService.updateTenantConsultationEntranceInfo(tenantConsultationEntranceDTO);
        return ResultDTO.success();
    }

    /**
     * 更新分账开关
     *
     * @param dto
     * @return
     */
    @PostMapping("/upsert/edit-psSwitch")
    public ResultDTO editProfitSharingSwitch(@RequestBody @Valid ProfitSharingSwitchDTO dto) {
        tenantService.editProfitSharingSwitch(dto);
        return ResultDTO.success();
    }

    /**
     * 查询分账角色信息
     *
     * @param tenantQueryDT
     * @return
     */
    @PostMapping("/query/profit-sharing-role-info")
    public CommonResult queryProfitSharingRoleInfo(@RequestBody TenantQueryDTO tenantQueryDT) {
        List<ProfitSharingRoleInfoVO> profitSharingRoleInfoVOS = tenantService.queryProfitSharingRoleInfo(tenantQueryDT.getTenantId ());
        return CommonResult.ok(profitSharingRoleInfoVOS);
    }


    /**
     * 新增品牌商城信息
     *
     * @param tenantAddMainInfoDTO
     * @return
     */
    //@RequiresPermissions(value = {"cosfo_oms:customer-brand-mall:add"}, logical = Logical.OR)
    @PostMapping("/upsert/add-tenant-info")
    public CommonResult<Boolean> insertTenantInfo(@RequestBody @Validated({ValidGroup.Insert.class}) TenantAddMainInfoDTO tenantAddMainInfoDTO) {
        TenantAddMainInfoServiceDTO dto = TenantMainInfoConvert.INSTANCE.convert2ServiceDto(tenantAddMainInfoDTO);
        dto.setLoginInfo(getMerchantInfoDTO());
        tenantService.insertTenantInfo(dto);
        tenantService.sendMqMsgForInsertTenantInfo(dto);
        return CommonResult.ok(Boolean.TRUE);
    }


    /**
     * 查询品牌方/供应商列表
     * 0品牌方 1 供应商
     *
     * @return
     */
    @PostMapping("/query/tenant/{type}")
    public CommonResult<List<TenantVO>> queryTenantList(@PathVariable("type") Integer type) {
        return CommonResult.ok(tenantService.queryTenantByType(type));
    }


    /**
     * 获取品牌方对应供应商
     *
     * @param tenantId
     * @return
     */
    @PostMapping("/query/supplier/{tenantId}")
    public CommonResult<List<SupplierTenantVO>> querySupplierList(@PathVariable("tenantId") Long tenantId) {
        return CommonResult.ok(tenantService.querySupplierList(tenantId));
    }

    @PostMapping("/upsert/init")
    public CommonResult initSupplierDistributorRole(@RequestBody TenantDTO tenantDTO) {
        tenantService.initSupplierDistributorRole(tenantDTO.getId());
        return CommonResult.ok();
    }
    /**
     * 获取品牌方对应省心定列表
     *
     * @return
     */
    @PostMapping("/query/list/save-worry")
    public CommonResult<PageInfo<TenantVO>> querySaveWorryList(@RequestBody TenantQueryDTO tenantQueryDTO) {
        return CommonResult.ok(tenantService.querySaveWorryList(tenantQueryDTO));
    }
    /**
     * 获取品牌方对应省心定开关
     * 省心订开关 0:关闭 1开启
     *
     * @param tenantId
     * @return
     */
    @PostMapping("/query/save-worry-switch/{tenantId}")
    public CommonResult<Integer> querySaveWorrySwitch(@PathVariable("tenantId") Long tenantId) {
        return CommonResult.ok(tenantService.querySaveWorrySwitch(tenantId));
    }
    /**
     * 更新品牌方对应省心定开关
     *
     * @return
     */
    @PostMapping("/upsert/save-worry-switch")
    public CommonResult saveOrUpdateSaveWorrySwitch(@RequestBody @Validated({ValidGroup.Update.class})  TenantSaveWorrySwitchDTO dto) {
        tenantService.saveOrUpdateSaveWorrySwitch(dto);
        return CommonResult.ok();
    }
}
