package com.cosfo.oms.tenant.convert;

import com.cosfo.oms.common.context.IndirectChannelTypeEnum;
import com.cosfo.oms.common.context.WechatAppletPayChannelEnum;
import com.cosfo.oms.common.result.ResultDTOEnum;
import com.cosfo.oms.common.utils.AssertBiz;
import com.cosfo.oms.tenant.model.dto.TenantAuthDTO;
import com.cosfo.oms.tenant.model.dto.TenantAuthServiceDTO;
import com.cosfo.oms.tenant.model.po.TenantAuthConnection;
import com.cosfo.oms.tenant.model.vo.TenantAuthVO;
import org.apache.commons.lang3.tuple.Triple;

/**
 * @Author: fansongsong
 * @Date: 2023-11-10
 * @Description:
 */
public class TenantAuthConvert {

    public static TenantAuthVO convert2AuthVo(TenantAuthConnection entity) {
        if (entity == null) {
            return null;
        }

        TenantAuthVO tenantAuthVO = new TenantAuthVO();

        tenantAuthVO.setId(entity.getId());
        tenantAuthVO.setAppId(entity.getAppId());
        tenantAuthVO.setTenantId(entity.getTenantId());
        tenantAuthVO.setPayMchid(entity.getPayMchid());
        tenantAuthVO.setPaySecret(entity.getPaySecret());
        tenantAuthVO.setPayCertPath(entity.getPayCertPath());
        tenantAuthVO.setAccountType(entity.getAccountType());
        tenantAuthVO.setHuifuId(entity.getHuifuId());
        tenantAuthVO.setSecretKey(entity.getSecretKey());
        tenantAuthVO.setPublicKey(entity.getPublicKey());
        tenantAuthVO.setHuifuPublicKey(entity.getHuifuPublicKey());
        tenantAuthVO.setWechatDirectSwitch(entity.getWechatDirectSwitch());
        tenantAuthVO.setWechatIndirectSwitch(entity.getWechatIndirectSwitch());
        tenantAuthVO.setWechatIndirectSharingSwitch(entity.getWechatIndirectSharingSwitch());
        tenantAuthVO.setOaAppId(entity.getOaAppId());
        tenantAuthVO.setOaAppSecret(entity.getOaAppSecret());
        tenantAuthVO.setAliIndirectSwitch(entity.getAliIndirectSwitch());
        tenantAuthVO.setAliIndirectSharingSwitch(entity.getAliIndirectSharingSwitch());
        tenantAuthVO.setH5WechatIndirectSwitch(entity.getH5WechatIndirectSwitch());
        tenantAuthVO.setH5WechatIndirectSharingSwitch(entity.getH5WechatIndirectSharingSwitch());
        tenantAuthVO.setWechatIndirectPluginSwitch(entity.getWechatIndirectPluginSwitch());
        tenantAuthVO.setAppletWechatPaySwitch(entity.getAppletWechatPaySwitch());
        Integer appletPayChannel = WechatAppletPayChannelEnum.getCodeByConfig(entity.getWechatDirectSwitch(),
                entity.getWechatIndirectSwitch(), entity.getWechatIndirectPluginSwitch());
        tenantAuthVO.setWechatAppletPayChannel(appletPayChannel);

        return tenantAuthVO;
    }

    public static TenantAuthServiceDTO convert2ServiceDto(TenantAuthDTO dto) {
        if (dto == null) {
            return null;
        }

        TenantAuthServiceDTO tenantAuthServiceDTO = new TenantAuthServiceDTO();

        tenantAuthServiceDTO.setId(dto.getId());
        tenantAuthServiceDTO.setTenantId(dto.getTenantId());
        tenantAuthServiceDTO.setPayMchid(dto.getPayMchid());
        tenantAuthServiceDTO.setPaySecret(dto.getPaySecret());
        tenantAuthServiceDTO.setPayCertPath(dto.getPayCertPath());
        tenantAuthServiceDTO.setHuifuId(dto.getHuifuId());
        tenantAuthServiceDTO.setSecretKey(dto.getSecretKey());
        tenantAuthServiceDTO.setPublicKey(dto.getPublicKey());
        tenantAuthServiceDTO.setHuifuPublicKey(dto.getHuifuPublicKey());
        tenantAuthServiceDTO.setWechatIndirectSharingSwitch(dto.getWechatIndirectSharingSwitch());
        tenantAuthServiceDTO.setOaAppId(dto.getOaAppId());
        tenantAuthServiceDTO.setOaAppSecret(dto.getOaAppSecret());
        tenantAuthServiceDTO.setAliIndirectSwitch(dto.getAliIndirectSwitch());
        tenantAuthServiceDTO.setAliIndirectSharingSwitch(dto.getAliIndirectSharingSwitch());
        tenantAuthServiceDTO.setH5WechatIndirectSwitch(dto.getH5WechatIndirectSwitch());
        tenantAuthServiceDTO.setH5WechatIndirectSharingSwitch(dto.getH5WechatIndirectSharingSwitch());
        tenantAuthServiceDTO.setAppletWechatPaySwitch(dto.getAppletWechatPaySwitch());

        Integer wechatAppletPayChannel = dto.getWechatAppletPayChannel();
        WechatAppletPayChannelEnum wechatAppletPayChannelEnum = WechatAppletPayChannelEnum.getByCode(wechatAppletPayChannel);
        AssertBiz.notNull(wechatAppletPayChannelEnum, ResultDTOEnum.PARAMETER_MISSING.getCode(),
                "wechatAppletPayChannel参数异常");
        Triple<Integer, Integer, Integer> configByParam = WechatAppletPayChannelEnum.getConfigByEnum(wechatAppletPayChannelEnum);
        Integer wechatDirectSwitch = configByParam.getLeft();
        Integer wechatIndirectSwitch = configByParam.getMiddle();
        Integer wechatIndirectPluginSwitch = configByParam.getRight();

        tenantAuthServiceDTO.setWechatDirectSwitch(wechatDirectSwitch);
        tenantAuthServiceDTO.setWechatIndirectSwitch(wechatIndirectSwitch);
        tenantAuthServiceDTO.setWechatIndirectPluginSwitch(wechatIndirectPluginSwitch);
        tenantAuthServiceDTO.setIndirectOnlineChannel(dto.getIndirectOnlineChannel());
        return tenantAuthServiceDTO;
    }

}
