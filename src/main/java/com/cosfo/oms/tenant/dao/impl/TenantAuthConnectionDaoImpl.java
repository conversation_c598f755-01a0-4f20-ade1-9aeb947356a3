package com.cosfo.oms.tenant.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.oms.common.context.tenant.TenantCreateTemplateStatus;
import com.cosfo.oms.tenant.convert.TenantMainInfoConvert;
import com.cosfo.oms.tenant.dao.TenantAuthConnectionDao;
import com.cosfo.oms.tenant.mapper.TenantAuthConnectionMapper;
import com.cosfo.oms.tenant.model.dto.TenantAuthAppIdDTO;
import com.cosfo.oms.tenant.model.dto.TenantAuthConnQueryDTO;
import com.cosfo.oms.tenant.model.dto.TenantAuthServiceDTO;
import com.cosfo.oms.tenant.model.po.TenantAuthConnection;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

import static com.cosfo.oms.common.constant.TenantDefaultConstant.DEFAULT_STATUS;

/**
 * (TenantAuthConnection)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-29 16:27:46
 */
@Service
public class TenantAuthConnectionDaoImpl extends ServiceImpl<TenantAuthConnectionMapper, TenantAuthConnection> implements TenantAuthConnectionDao {


    @Override
    public List<TenantAuthConnection> listByParam(TenantAuthConnQueryDTO dto) {
        LambdaQueryWrapper<TenantAuthConnection> query = buildQueryWrapper(dto);

        // 添加分页支持
        if (dto.getOffset() != null && dto.getLimit() != null) {
            query.last("LIMIT " + dto.getOffset() + ", " + dto.getLimit());
        }

        return list(query);
    }

    private LambdaQueryWrapper<TenantAuthConnection> buildQueryWrapper(TenantAuthConnQueryDTO dto) {
        LambdaQueryWrapper<TenantAuthConnection> query = new LambdaQueryWrapper<>();
        query.eq(Objects.nonNull(dto.getTenantId()), TenantAuthConnection::getTenantId, dto.getTenantId());
        query.orderByAsc(TenantAuthConnection::getId); // 确保分页结果稳定
        return query;
    }

    @Override
    public void saveOrUpdateTenantAuthConn(TenantAuthServiceDTO dto) {
        TenantAuthConnection record = TenantMainInfoConvert.INSTANCE.convert2Entity(dto);
        if (Objects.isNull(dto.getId())) {
            record.setStatus(DEFAULT_STATUS);
            record.setCreateTemplateStatus(TenantCreateTemplateStatus.INIT.getStatus());
            baseMapper.insertSelective(record);
        } else {
            LambdaUpdateWrapper<TenantAuthConnection> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(TenantAuthConnection::getPayMchid, dto.getPayMchid());
            updateWrapper.set(TenantAuthConnection::getPaySecret, dto.getPaySecret());
            updateWrapper.set(TenantAuthConnection::getPayCertPath, dto.getPayCertPath());
            updateWrapper.set(TenantAuthConnection::getHuifuId, dto.getHuifuId());
            updateWrapper.set(TenantAuthConnection::getSecretKey, dto.getSecretKey());
            updateWrapper.set(TenantAuthConnection::getPublicKey, dto.getPublicKey());
            updateWrapper.set(TenantAuthConnection::getHuifuPublicKey, dto.getHuifuPublicKey());
            updateWrapper.set(TenantAuthConnection::getWechatDirectSwitch, dto.getWechatDirectSwitch());
            updateWrapper.set(TenantAuthConnection::getWechatIndirectSwitch, dto.getWechatIndirectSwitch());
            updateWrapper.set(TenantAuthConnection::getWechatIndirectSharingSwitch, dto.getWechatIndirectSharingSwitch());
            updateWrapper.set(TenantAuthConnection::getOaAppId, dto.getOaAppId());
            updateWrapper.set(TenantAuthConnection::getOaAppSecret, dto.getOaAppSecret());
            updateWrapper.set(TenantAuthConnection::getAliIndirectSwitch, dto.getAliIndirectSwitch());
            updateWrapper.set(TenantAuthConnection::getAliIndirectSharingSwitch, dto.getAliIndirectSharingSwitch());

            updateWrapper.set(TenantAuthConnection::getH5WechatIndirectSwitch, dto.getH5WechatIndirectSwitch());
            updateWrapper.set(TenantAuthConnection::getH5WechatIndirectSharingSwitch, dto.getH5WechatIndirectSharingSwitch());
            updateWrapper.set(TenantAuthConnection::getWechatIndirectPluginSwitch, dto.getWechatIndirectPluginSwitch());
            updateWrapper.set(TenantAuthConnection::getAppletWechatPaySwitch, dto.getAppletWechatPaySwitch());
            updateWrapper.eq(TenantAuthConnection::getId, dto.getId());
            update(record, updateWrapper);
        }
    }

    @Override
    public void savaOrUpdateTenantAuthConnAppId(TenantAuthAppIdDTO dto) {
        TenantAuthConnection record = TenantMainInfoConvert.INSTANCE.convert2Entity(dto);
        if (dto.getId() == null) {
            record.setStatus(DEFAULT_STATUS);
            record.setCreateTemplateStatus(TenantCreateTemplateStatus.INIT.getStatus());
            baseMapper.insertSelective(record);
        } else {
            LambdaUpdateWrapper<TenantAuthConnection> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(TenantAuthConnection::getAppId, dto.getAppId());
            updateWrapper.eq(TenantAuthConnection::getId, dto.getId());
            update(record, updateWrapper);
        }
    }
}
