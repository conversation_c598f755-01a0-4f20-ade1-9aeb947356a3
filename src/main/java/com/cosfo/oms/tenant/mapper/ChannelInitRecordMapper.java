package com.cosfo.oms.tenant.mapper;

import com.cosfo.oms.tenant.model.po.ChannelInitRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 渠道初始化记录Mapper
 * 
 * <AUTHOR>
 * @date 2025-08-18
 */
@Mapper
public interface ChannelInitRecordMapper {
    
    /**
     * 插入记录
     */
    int insert(ChannelInitRecord record);
    
    /**
     * 批量插入记录
     */
    int batchInsert(@Param("records") List<ChannelInitRecord> records);
    
    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(ChannelInitRecord record);
    
    /**
     * 根据主键查询
     */
    ChannelInitRecord selectByPrimaryKey(Long id);
    
    /**
     * 根据租户ID、认证连接ID、渠道类型查询
     */
    ChannelInitRecord selectByTenantAndChannelType(@Param("tenantId") Long tenantId, 
                                                   @Param("authConnectionId") Long authConnectionId,
                                                   @Param("channelType") String channelType);
    
    /**
     * 查询指定租户的初始化记录
     */
    List<ChannelInitRecord> selectByTenantId(@Param("tenantId") Long tenantId);
    
    /**
     * 查询指定状态的记录
     */
    List<ChannelInitRecord> selectByInitStatus(@Param("initStatus") Integer initStatus,
                                               @Param("limit") Integer limit);
    
    /**
     * 统计初始化记录数量
     */
    int countByTenantAndStatus(@Param("tenantId") Long tenantId, 
                               @Param("initStatus") Integer initStatus);
}
