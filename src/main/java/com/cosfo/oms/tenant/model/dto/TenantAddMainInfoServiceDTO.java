package com.cosfo.oms.tenant.model.dto;

import com.cosfo.oms.model.dto.LoginContextInfoDTO;
import lombok.Data;


/**
 * @author: monna.chen
 * @Date: 2023/3/28 17:51
 * @Description:
 */
@Data
public class TenantAddMainInfoServiceDTO {
    /**
     * 品牌商城ID
     */
    private Long tenantId;

    /**
     * 鲜沐大客户Id
     */
    private Long adminId;

    /**
     * 登录手机号
     */
    private String loginPhone;

    /**
     * email
     */
    private String email;

    /**
     * 租户账户类型:0-手机号登录，1-邮箱登录
     */
    private Integer accountLoginType;

    /**
     * 租户类型：0-品牌方,1-鲜沐,2-帆台,3-外单
     */
    private Integer type;

    /**
     * 商城名称
     */
    private String merchantName;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 租户联系人名称
     */
    private String contactName;

    /**
     * 信用代码
     */
    private String creditCode;

    /**
     * 营业执照
     */
    private String businessLicense;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 街道地址
     */
    private String address;

    /**
     * 公司联系手机号
     */
    private String companyPhone;

    /**
     * 联系电话-区号
     */
    private String companyAreaPhone;

    /**
     * 登录信息
     */
    private LoginContextInfoDTO loginInfo;

    /**
     * 版本权益信息
     */
    private TenantPrivilegesConfigDTO privilegesConfigDTO;


    /**
     * 客户经理
     */
    private String customerManager;

    /**
     * 货币符号1:人民币 2:RM 默认人民币
     */
    private Integer currency;
}
