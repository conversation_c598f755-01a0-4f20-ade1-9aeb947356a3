package com.cosfo.oms.tenant.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * @author: monna.chen
 * @Date: 2023/3/29 16:29
 * @Description:
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TenantAuthConnQueryDTO {

    /**
     * appId
     */
    private String appId;

    /**
     * tenant_id
     */
    @NotNull(message = "租户ID 不能为空", groups = {ValidGroup.Update.class})
    private Long tenantId;

    /**
     * 分页偏移量
     */
    private Integer offset;

    /**
     * 分页大小
     */
    private Integer limit;
}
