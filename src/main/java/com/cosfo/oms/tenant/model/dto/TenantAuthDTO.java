package com.cosfo.oms.tenant.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: monna.chen
 * @Date: 2023/3/27 18:41
 * @Description:  支付配置
 */
@Data
public class TenantAuthDTO {
    /**
     * primary key
     */
    private Long id;

    /**
     * 租户id
     */
    @NotNull(message = "租户ID 不能为空", groups = {ValidGroup.Update.class, ValidGroup.Insert.class})
    private Long tenantId;

    /**
     * 微信商户号
     */
    private String payMchid;

    /**
     * 微信支付密钥
     */
    private String paySecret;

    /**
     * 微信证书路径
     */
    private String payCertPath;

    /**
     * 汇付商户号
     */
    private String huifuId;

    /**
     * 汇付商户私钥
     */
    private String secretKey;

    /**
     * 汇付商户公钥
     */
    private String publicKey;

    /**
     * 汇付渠道公钥
     */
    private String huifuPublicKey;


    /**
     * 小程序微信间连分账开关 0、关闭 1、开启
     */
    private Integer wechatIndirectSharingSwitch;

    /**
     * 公众号id
     */
    private String oaAppId;

    /**
     * 公众号密钥
     */
    private String oaAppSecret;

    /**
     * H5支付宝间连开关 0、关闭 1、开启
     */
    private Integer aliIndirectSwitch;

    /**
     * H5支付宝间连开关 0、关闭 1、开启
     */
    private Integer aliIndirectSharingSwitch;

    /**
     * H5微信间连开关 0、关闭 1、开启
     */
    private Integer h5WechatIndirectSwitch;

    /**
     * H5微信间连分账开关 0、关闭 1、开启
     */
    private Integer h5WechatIndirectSharingSwitch;

    /**
     * 小程序微信支付开关 0、关闭 1开启
     */
    private Integer appletWechatPaySwitch;

    /**
     * 小程序支付通道 1:微信间连-汇付;2:微信间连-汇付(插件);3:微信直连
     */
    private Integer wechatAppletPayChannel;

    /**
     * 间连支付渠道 1、汇付 2、智付
     */
    private Integer indirectOnlineChannel;

}
