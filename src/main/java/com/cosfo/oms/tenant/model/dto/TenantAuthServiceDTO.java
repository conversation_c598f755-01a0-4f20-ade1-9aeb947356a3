package com.cosfo.oms.tenant.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: monna.chen
 * @Date: 2023/3/30 11:37
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TenantAuthServiceDTO {

    /**
     * primary key
     */
    private Long id;

    /**
     * 小程序app_id
     */
    private String appId;

    /**
     * 商户类型 1：企业 2：个人
     */
    private String accountType;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 微信商户号
     */
    private String payMchid;

    /**
     * 微信支付密钥
     */
    private String paySecret;

    /**
     * 微信证书路径
     */
    private String payCertPath;

    /**
     * 汇付商户号
     */
    private String huifuId;

    /**
     * 汇付商户私钥
     */
    private String secretKey;

    /**
     * 汇付商户公钥
     */
    private String publicKey;

    /**
     * 汇付渠道公钥
     */
    private String huifuPublicKey;

    /**
     * 微信直连开关 0、关闭 1、开启
     */
    private Integer wechatDirectSwitch;

    /**
     * 微信间连开关 0、关闭 1、开启
     */
    private Integer wechatIndirectSwitch;

    /**
     * 微信间连分账开关 0、关闭 1、开启
     */
    private Integer wechatIndirectSharingSwitch;

    /**
     * 公众号id
     */
    private String oaAppId;

    /**
     * 公众号密钥
     */
    private String oaAppSecret;

    /**
     * 支付宝间连开关 0、关闭 1、开启
     */
    private Integer aliIndirectSwitch;

    /**
     * 支付宝间连分账开关 0、关闭 1、开启
     */
    private Integer aliIndirectSharingSwitch;

    /**
     * H5微信间连开关 0、关闭 1、开启
     */
    private Integer h5WechatIndirectSwitch;

    /**
     * H5微信间连分账开关 0、关闭 1、开启
     */
    private Integer h5WechatIndirectSharingSwitch;

    /**
     * 小程序微信间联-汇付插件开关 0、关闭 1、开启
     */
    private Integer wechatIndirectPluginSwitch;

    /**
     * 小程序微信支付开关 0、关闭 1开启
     */
    private Integer appletWechatPaySwitch;

    /**
     * 间连支付渠道 1、汇付 2、智付
     */
    private Integer indirectOnlineChannel;

    /**
     * 智付商户号
     */
    private String dinMerchantNo;

    /**
     * 智付公钥
     */
    private String dinPublicKey;

    /**
     * 智付私钥
     */
    private String dinPrivateKey;

    /**
     * 智付对称密钥
     */
    private String dinSecret;

}
