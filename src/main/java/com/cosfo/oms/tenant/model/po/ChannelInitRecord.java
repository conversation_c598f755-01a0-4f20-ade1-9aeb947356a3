package com.cosfo.oms.tenant.model.po;

import lombok.Data;

import java.util.Date;

/**
 * 渠道初始化记录表
 * 
 * <AUTHOR>
 * @date 2025-08-18
 */
@Data
public class ChannelInitRecord {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 租户ID
     */
    private Long tenantId;
    
    /**
     * 租户认证连接ID
     */
    private Long authConnectionId;
    
    /**
     * 渠道类型：WECHAT_DIRECT-微信直连，HUIFU_INDIRECT-汇付间连
     */
    private String channelType;
    
    /**
     * 中央服务返回的渠道ID
     */
    private Long channelId;
    
    /**
     * 初始化状态：0-待初始化，1-初始化成功，2-初始化失败
     */
    private Integer initStatus;
    
    /**
     * 初始化时间
     */
    private Date initTime;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 重试次数
     */
    private Integer retryCount;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 渠道类型枚举
     */
    public enum ChannelType {
        WECHAT_DIRECT("WECHAT_DIRECT", "微信直连"),
        HUIFU_INDIRECT("HUIFU_INDIRECT", "汇付间连");
        
        private final String code;
        private final String desc;
        
        ChannelType(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDesc() {
            return desc;
        }
    }
    
    /**
     * 初始化状态枚举
     */
    public enum InitStatus {
        PENDING(0, "待初始化"),
        SUCCESS(1, "初始化成功"),
        FAILED(2, "初始化失败");
        
        private final Integer code;
        private final String desc;
        
        InitStatus(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }
        
        public Integer getCode() {
            return code;
        }
        
        public String getDesc() {
            return desc;
        }
    }
}
