package com.cosfo.oms.tenant.model.po;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * tenant_auth_connection
 *
 * <AUTHOR>
@Data
@TableName("tenant_auth_connection")
public class TenantAuthConnection implements Serializable {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 授权人APPID
     */
    private String appId;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 1-正常
     */
    private Integer status;

    /**
     * 账号类型
     */
    private String accountType;

    /**
     * 直连支付商户好
     */
    private String payMchid;

    /**
     * 支付秘钥
     */
    private String paySecret;

    /**
     * 证书路径
     */
    private String payCertPath;

    /**
     * 汇付商户号
     */
    private String huifuId;

    /**
     * 汇付秘钥
     */
    private String secretKey;

    /**
     * 公钥
     */
    private String publicKey;

    /**
     * 汇付公钥
     */
    private String huifuPublicKey;

    /**
     * 支付渠道配置ID, 对应渠道路由管理服务中的主键
     */
    private Long paymentChannelId;

    /**
     * 小程序微信直连开关 0、关闭 1、开启
     */
    private Integer wechatDirectSwitch;

    /**
     * 小程序微信间连开关 0、关闭 1、开启
     */
    private Integer wechatIndirectSwitch;

    /**
     * 小程序微信间连分账开关 0、关闭 1、开启
     */
    private Integer wechatIndirectSharingSwitch;

    /**
     * 公众号id
     */
    private String oaAppId;

    /**
     * 公众号密钥
     */
    private String oaAppSecret;

    /**
     * H5支付宝间连开关 0、关闭 1、开启
     */
    private Integer aliIndirectSwitch;

    /**
     * H5支付宝间连分账开关 0、关闭 1、开启
     */
    private Integer aliIndirectSharingSwitch;

    /**
     * H5微信间连开关 0、关闭 1、开启
     */
    private Integer h5WechatIndirectSwitch;

    /**
     * H5微信间连分账开关 0、关闭 1、开启
     */
    private Integer h5WechatIndirectSharingSwitch;

    /**
     * 小程序微信间联-汇付插件开关 0、关闭 1、开启
     */
    private Integer wechatIndirectPluginSwitch;

    /**
     * 小程序微信支付开关 0、关闭 1开启
     */
    private Integer appletWechatPaySwitch;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     *上次同步结算信息时间
     */
    private LocalDateTime syncTime;

    /**
     * 创建模版状态,1:待发起创建;2:已发起创建
     */
    private Integer createTemplateStatus;

    private static final long serialVersionUID = 1L;
}