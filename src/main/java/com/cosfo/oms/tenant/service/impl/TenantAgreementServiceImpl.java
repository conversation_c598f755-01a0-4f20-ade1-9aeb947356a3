package com.cosfo.oms.tenant.service.impl;

import com.cosfo.oms.common.result.ResultDTO;
import com.cosfo.oms.tenant.mapper.TenantAgreementMapper;
import com.cosfo.oms.tenant.model.po.TenantAgreement;
import com.cosfo.oms.tenant.service.TenantAgreementService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/28 15:29
 */
@Service
public class TenantAgreementServiceImpl implements TenantAgreementService {

    @Resource
    private TenantAgreementMapper tenantAgreementMapper;

    @Override
    public ResultDTO listAll(Integer type) {
        TenantAgreement agreement = tenantAgreementMapper.selectByType(type);
        return ResultDTO.success(agreement);
    }
}
