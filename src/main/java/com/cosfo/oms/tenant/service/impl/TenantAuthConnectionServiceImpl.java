package com.cosfo.oms.tenant.service.impl;

import com.cosfo.oms.common.context.IndirectChannelTypeEnum;
import com.cosfo.oms.common.context.PaymentMethodSwitchEnum;
import com.cosfo.oms.common.context.PaymentSceneEnum;
import com.cosfo.oms.common.context.WxAccountTypeEnum;
import com.cosfo.oms.common.utils.AssertParam;
import com.cosfo.oms.facade.payment.PaymentChannelFacade;
import com.cosfo.oms.facade.UserCenterTenantAccountFacade;
import com.cosfo.oms.model.dto.LoginContextInfoDTO;
import com.cosfo.oms.tenant.convert.TenantAuthConvert;
import com.cosfo.oms.tenant.dao.TenantAuthConnectionDao;
import com.cosfo.oms.tenant.mapper.TenantAuthConnectionMapper;
import com.cosfo.oms.tenant.model.dto.TenantAuthAppIdDTO;
import com.cosfo.oms.tenant.model.dto.TenantAuthConnQueryDTO;
import com.cosfo.oms.tenant.model.dto.TenantAuthServiceDTO;
import com.cosfo.oms.tenant.model.po.TenantAuthConnection;
import com.cosfo.oms.tenant.model.vo.TenantAuthVO;
import com.cosfo.oms.tenant.service.TenantAuthConnectionService;
import com.cosfo.oms.tenant.service.TenantInfoService;
import com.cosfo.oms.tenant.service.TenantPayConfigLogService;
import com.cosfo.oms.tenant.service.TenantService;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.payment.routing.common.enums.PaymentBusinessLineEnums;
import net.summerfarm.payment.routing.model.dto.PaymentRoutingDTO;
import net.summerfarm.payment.routing.model.dto.PaymentRoutingQueryDTO;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.usercenter.client.tenant.resp.TenantResultResp;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class TenantAuthConnectionServiceImpl implements TenantAuthConnectionService {

    @Resource
    private TenantAuthConnectionMapper tenantAuthConnectionMapper;
    @Resource
    private TenantAuthConnectionDao tenantAuthConnectionDao;
    @Resource
    private TenantService tenantService;
    @Resource
    private TenantInfoService tenantInfoService;
    @Resource
    private TenantPayConfigLogService tenantPayConfigLogService;
    @Resource
    private UserCenterTenantAccountFacade userCenterTenantAccountFacade;
    @Resource
    private PaymentChannelFacade paymentChannelFacade;

    @Override
    public int insertAuthorizer(String appId, Long tenantId) {
        TenantAuthConnection tenantAuthConnection = new TenantAuthConnection();
        tenantAuthConnection.setAppId(appId);
        tenantAuthConnection.setTenantId(tenantId);
        tenantAuthConnection.setStatus(1);
        tenantAuthConnection.setCreateTime(new Date());
        return tenantAuthConnectionMapper.insertSelective(tenantAuthConnection);
    }

    @Override
    public TenantAuthConnection selectAuthorizer(String appId) {
        return tenantAuthConnectionMapper.selectByAppId(appId);
    }

    @Override
    public int updateAuthorizer(Long id, String appId, Long tenantId, Integer status) {
        TenantAuthConnection tenantAuthConnection = new TenantAuthConnection();
        tenantAuthConnection.setId(id);
        tenantAuthConnection.setAppId(appId);
        tenantAuthConnection.setTenantId(tenantId);
        tenantAuthConnection.setStatus(status);
        return tenantAuthConnectionMapper.updateByPrimaryKeySelective(tenantAuthConnection);
    }

    @Override
    public int updateAuthorizerStatus(TenantAuthConnection authConnection) {
        return tenantAuthConnectionMapper.updateByPrimaryKeySelective(authConnection);
    }

    @Override
    public TenantAuthVO selectAuthorizer(Long tenantId) {
        if (Objects.isNull(tenantId)) {
            throw new BizException("请指定租户查询！");
        }
        List<TenantAuthConnection> tenantAuthConnections = tenantAuthConnectionDao.listByParam(TenantAuthConnQueryDTO.builder()
                .tenantId(tenantId)
                .build());
        if (CollectionUtils.isEmpty(tenantAuthConnections)) {
            return null;
        }
        TenantAuthConnection tenantAuthConnection = tenantAuthConnections.get(0);
        TenantAuthVO tenantAuthVO = TenantAuthConvert.convert2AuthVo(tenantAuthConnection);
        // 加载渠道信息
        loadChannelInfo(tenantAuthConnection, tenantAuthVO);
        return tenantAuthVO;
    }

    private void loadChannelInfo(TenantAuthConnection tenantAuthConnection, TenantAuthVO tenantAuthVO) {
        log.info("开始加载租户支付渠道信息，租户ID: {}", tenantAuthConnection.getTenantId());

        // 根据场景开关，查询中心服务RPC
        Integer wechatDirectSwitch = tenantAuthConnection.getWechatDirectSwitch(); // 微信直连开关
        Integer wechatIndirectSwitch = tenantAuthConnection.getWechatIndirectSwitch(); // 微信间连开关
        Integer wechatIndirectPluginSwitch = tenantAuthConnection.getWechatIndirectPluginSwitch(); // 微信间连插件开关
        Integer h5WechatIndirectSwitch = tenantAuthConnection.getH5WechatIndirectSwitch(); // H5微信支付开关
        Integer aliIndirectSwitch = tenantAuthConnection.getAliIndirectSwitch(); // 支付宝间连开关
        Integer appletWechatPaySwitch = tenantAuthConnection.getAppletWechatPaySwitch(); // 小程序微信支付总开关

        // 构建路由查询参数列表
        List<PaymentRoutingQueryDTO> routingQueries = buildRoutingQueries(tenantAuthConnection);

        if (CollectionUtils.isEmpty(routingQueries)) {
            log.warn("租户{}没有启用任何支付场景，跳过路由信息查询", tenantAuthConnection.getTenantId());
            return;
        }

        log.info("租户{}启用了{}个支付场景，开始查询路由信息", tenantAuthConnection.getTenantId(), routingQueries.size());

        // 查询每个场景的路由信息
        for (PaymentRoutingQueryDTO queryDTO : routingQueries) {
            try {
                PaymentRoutingDTO routingInfo = paymentChannelFacade.getRoutingInfo(queryDTO);
                if (routingInfo != null) {
                    log.info("成功获取路由信息，租户ID: {}, 场景: {}, 渠道: {}",
                        tenantAuthConnection.getTenantId(), queryDTO.getSceneName(), routingInfo.getChannelName());

                    // 将路由信息填充到 tenantAuthConnection 和 tenantAuthVO 中
                    fillChannelInfoFromRouting(tenantAuthConnection, tenantAuthVO, routingInfo, queryDTO);
                } else {
                    log.warn("未获取到路由信息，租户ID: {}, 场景: {}",
                        tenantAuthConnection.getTenantId(), queryDTO.getSceneName());
                }
            } catch (Exception e) {
                log.error("查询路由信息失败，租户ID: {}, 场景: {}",
                    tenantAuthConnection.getTenantId(), queryDTO.getSceneName(), e);
            }
        }

        log.info("完成加载租户支付渠道信息，租户ID: {}", tenantAuthConnection.getTenantId());
    }

    /**
     * 构建路由查询参数列表
     */
    private List<PaymentRoutingQueryDTO> buildRoutingQueries(TenantAuthConnection tenantAuthConnection) {
        List<PaymentRoutingQueryDTO> queries = new ArrayList<>();
        Long tenantId = tenantAuthConnection.getTenantId();

        // 1. 小程序微信支付场景
        if (PaymentMethodSwitchEnum.ENABLED.getCode().equals(tenantAuthConnection.getAppletWechatPaySwitch())) {
            // 1.1 微信直连
            if (PaymentMethodSwitchEnum.ENABLED.getCode().equals(tenantAuthConnection.getWechatDirectSwitch())) {
                PaymentRoutingQueryDTO query = new PaymentRoutingQueryDTO();
                query.setTenantId(tenantId);
                query.setBusinessLine(PaymentBusinessLineEnums.SAAS.getCode());
                query.setSceneName(PaymentSceneEnum.APPLET_WECHAT_PAY.getSceneName());
                query.setPlatform(PaymentSceneEnum.APPLET_WECHAT_PAY.getPlatform());
                query.setPaymentMethod(PaymentSceneEnum.APPLET_WECHAT_PAY.getPaymentMethod());
                queries.add(query);
                log.debug("添加小程序微信直连路由查询，租户ID: {}", tenantId);
            }

            // 1.2 微信间连
            if (PaymentMethodSwitchEnum.ENABLED.getCode().equals(tenantAuthConnection.getWechatIndirectSwitch()) ||
                PaymentMethodSwitchEnum.ENABLED.getCode().equals(tenantAuthConnection.getWechatIndirectPluginSwitch())) {
                PaymentRoutingQueryDTO query = new PaymentRoutingQueryDTO();
                query.setTenantId(tenantId);
                query.setBusinessLine(PaymentBusinessLineEnums.SAAS.getCode());
                query.setSceneName(PaymentSceneEnum.APPLET_WECHAT_PAY.getSceneName());
                query.setPlatform(PaymentSceneEnum.APPLET_WECHAT_PAY.getPlatform());
                query.setPaymentMethod(PaymentSceneEnum.APPLET_WECHAT_PAY.getPaymentMethod());
                queries.add(query);
                log.debug("添加小程序微信间连路由查询，租户ID: {}", tenantId);
            }
        }

        // 2. H5微信支付场景
        if (PaymentMethodSwitchEnum.ENABLED.getCode().equals(tenantAuthConnection.getH5WechatIndirectSwitch())) {
            PaymentRoutingQueryDTO query = new PaymentRoutingQueryDTO();
            query.setTenantId(tenantId);
            query.setBusinessLine(PaymentBusinessLineEnums.SAAS.getCode());
            query.setSceneName(PaymentSceneEnum.H5_WECHAT_PAY.getSceneName());
            query.setPlatform(PaymentSceneEnum.H5_WECHAT_PAY.getPlatform());
            query.setPaymentMethod(PaymentSceneEnum.H5_WECHAT_PAY.getPaymentMethod());
            queries.add(query);
            log.debug("添加H5微信支付路由查询，租户ID: {}", tenantId);
        }

        // 3. H5支付宝支付场景
        if (PaymentMethodSwitchEnum.ENABLED.getCode().equals(tenantAuthConnection.getAliIndirectSwitch())) {
            PaymentRoutingQueryDTO query = new PaymentRoutingQueryDTO();
            query.setTenantId(tenantId);
            query.setBusinessLine(PaymentBusinessLineEnums.SAAS.getCode());
            query.setSceneName(PaymentSceneEnum.H5_ALI_PAY.getSceneName());
            query.setPlatform(PaymentSceneEnum.H5_ALI_PAY.getPlatform());
            query.setPaymentMethod(PaymentSceneEnum.H5_ALI_PAY.getPaymentMethod());
            queries.add(query);
            log.debug("添加H5支付宝支付路由查询，租户ID: {}", tenantId);
        }

        return queries;
    }

    @Override
    public void saveOrUpdateAuth(TenantAuthServiceDTO dto, LoginContextInfoDTO loginContextInfoDTO) {
        log.info("开始保存租户支付配置，租户ID: {}", dto.getTenantId());

        // 1. 基础验证
        getTenantInfo(dto.getTenantId());
        List<TenantAuthConnection> tenantAuthConnections = getTenantAuthConnections(dto.getTenantId());

        if (CollectionUtils.isNotEmpty(tenantAuthConnections)) {
            dto.setId(tenantAuthConnections.get(0).getId());
        } else {
            dto.setId(null);
        }

        // 2. 效验支付配置参数是否有效
        authValidationCheck(dto);

        try {
            // 3. 通过 Facade 将多渠道信息保存到中央服务
            List<Long> channelIds = paymentChannelFacade.saveMultiChannelConfigs(dto);
            log.info("成功保存多渠道配置，租户ID: {}, 渠道数量: {}, 渠道IDs: {}",
                dto.getTenantId(), channelIds.size(), channelIds);

            // 4. 保存本地配置
            dto.setAccountType(WxAccountTypeEnum.PROPRIETARY.getDesc());
            tenantAuthConnectionDao.saveOrUpdateTenantAuthConn(dto);

            log.info("成功保存租户支付配置，租户ID: {}", dto.getTenantId());

        } catch (Exception e) {
            log.error("保存租户支付配置失败，租户ID: {}", dto.getTenantId(), e);
            throw new BizException("保存支付配置失败: " + e.getMessage());
        }

//        Map<Long, TenantAuthConnection> tenantAuthConnectionMap = tenantAuthConnections.stream().collect(Collectors.toMap(TenantAuthConnection::getId, item -> item));
//        TenantAuthConnection tenantAuthConnection = tenantAuthConnectionMap.get(dto.getId());

//        if (checkPayConfigHavingUpdated(tenantAuthConnection, dto)) {
//            TenantAccountResultResp tenantAccountInfo = userCenterTenantAccountFacade.getTenantAccountInfo(loginContextInfoDTO.getAuthUserId());
//            tenantPayConfigLogService.saveTenantPayConfigLog(tenantAuthConnection, dto, tenantAccountInfo.getId());
//        }
    }

    /**
     * 检查支付配置是否更新
     */
//    private Boolean checkPayConfigHavingUpdated(TenantAuthConnection tenantAuthConnection, TenantAuthServiceDTO dto) {
//        if (Objects.isNull(tenantAuthConnection)) {
//            return false;
//        }
//        return !Objects.equals(tenantAuthConnection.getPayMchid(), (dto.getPayMchid()))
//                || !Objects.equals(tenantAuthConnection.getPaySecret(), dto.getPaySecret())
//                || !Objects.equals(tenantAuthConnection.getPayCertPath(), dto.getPayCertPath())
//                || !Objects.equals(tenantAuthConnection.getHuifuId(), dto.getHuifuId())
//                || !Objects.equals(tenantAuthConnection.getSecretKey(), dto.getSecretKey())
//                || !Objects.equals(tenantAuthConnection.getPublicKey(), dto.getPublicKey())
//                || !Objects.equals(tenantAuthConnection.getHuifuPublicKey(), dto.getHuifuPublicKey());
//    }

    @Override
    public void saveOrUpdateAuthAppId(TenantAuthAppIdDTO dto) {
        getTenantInfo(dto.getTenantId());

        List<TenantAuthConnection> tenantAuthConnections = getTenantAuthConnections(dto.getTenantId());
        if (CollectionUtils.isNotEmpty(tenantAuthConnections)) {
            dto.setId(tenantAuthConnections.get(0).getId());
        } else {
            dto.setId(null);
        }
        tenantAuthConnectionDao.savaOrUpdateTenantAuthConnAppId(dto);
    }


    private TenantResultResp getTenantInfo(Long tenantId) {
        TenantResultResp tenantInfo = tenantInfoService.getTenantInfo(tenantId);
        if (tenantInfo == null) {
            throw new BizException("品牌商城不存在");
        }
        return tenantInfo;
    }

    private List<TenantAuthConnection> getTenantAuthConnections(Long tenantId) {
        return tenantAuthConnectionDao.listByParam(TenantAuthConnQueryDTO.builder()
                .tenantId(tenantId)
                .build());
    }

    /**
     * 检测支付配置是否有效
     */
    private void authValidationCheck(TenantAuthServiceDTO dto) {
        // 1. 直连支付校验
        if (PaymentMethodSwitchEnum.ENABLED.getCode().equals(dto.getWechatDirectSwitch())) {
            AssertParam.assertString(dto.getPayMchid(), "微信商户号不能为空");
            AssertParam.assertString(dto.getPaySecret(), "微信支付密钥不能为空");
            AssertParam.assertString(dto.getPayCertPath(), "微信证书路径不能为空");
        }

        // 2. 间连支付公共校验
        boolean isIndirect = PaymentMethodSwitchEnum.ENABLED.getCode().equals(dto.getWechatIndirectSwitch())
                || PaymentMethodSwitchEnum.ENABLED.getCode().equals(dto.getWechatIndirectPluginSwitch())
                || PaymentMethodSwitchEnum.ENABLED.getCode().equals(dto.getH5WechatIndirectSwitch())
                || PaymentMethodSwitchEnum.ENABLED.getCode().equals(dto.getAliIndirectSwitch());

        if (isIndirect) {
            if (Objects.isNull(dto.getIndirectOnlineChannel())) {
                throw new ParamsException("间连渠道类型不能为空");
            }

            // 3. 根据不同的间连渠道进行特定校验
            if (IndirectChannelTypeEnum.HUI_FU.getCode().equals(dto.getIndirectOnlineChannel())) {
                AssertParam.assertString(dto.getHuifuId(), "汇付商户商户号不能为空");
                AssertParam.assertString(dto.getSecretKey(), "汇付商户密钥不能为空");
                AssertParam.assertString(dto.getPublicKey(), "汇付商户公钥不能为空");
                AssertParam.assertString(dto.getHuifuPublicKey(), "汇付渠道公钥不能为空");
            } else if (IndirectChannelTypeEnum.DIN.getCode().equals(dto.getIndirectOnlineChannel())) {
                // 您需要取消注释并确保 TenantAuthServiceDTO 中有 dinId, dinSecretKey, dinPublicKey, dinSymmetricKey 这些字段
                 AssertParam.assertString(dto.getDinMerchantNo(), "智付商户号不能为空");
                 AssertParam.assertString(dto.getDinPrivateKey(), "智付私钥不能为空");
                 AssertParam.assertString(dto.getDinPublicKey(), "智付公钥不能为空");
                 AssertParam.assertString(dto.getDinSecret(), "智付密钥不能为空");
            }
        }

        // 4. H5微信支付特定校验
        if (PaymentMethodSwitchEnum.ENABLED.getCode().equals(dto.getH5WechatIndirectSwitch())) {
            AssertParam.assertString(dto.getOaAppId(), "公众号appId不能为空");
            AssertParam.assertString(dto.getOaAppSecret(), "公众号密钥不能为空");
        }

        // 5. 分账开关逻辑
        if (PaymentMethodSwitchEnum.DISABLED.getCode().equals(dto.getWechatIndirectSwitch())
                && PaymentMethodSwitchEnum.DISABLED.getCode().equals(dto.getWechatIndirectPluginSwitch())) {
            dto.setWechatIndirectSharingSwitch(PaymentMethodSwitchEnum.DISABLED.getCode());
        }

        if (dto.getAliIndirectSwitch().equals(PaymentMethodSwitchEnum.DISABLED.getCode())) {
            dto.setAliIndirectSharingSwitch(PaymentMethodSwitchEnum.DISABLED.getCode());
        }

        if (PaymentMethodSwitchEnum.DISABLED.getCode().equals(dto.getH5WechatIndirectSwitch())) {
            dto.setH5WechatIndirectSharingSwitch(PaymentMethodSwitchEnum.DISABLED.getCode());
        }
    }
}