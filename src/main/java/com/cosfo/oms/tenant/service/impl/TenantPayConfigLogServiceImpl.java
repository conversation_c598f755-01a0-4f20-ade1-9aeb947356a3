package com.cosfo.oms.tenant.service.impl;

import com.cosfo.oms.tenant.dao.TenantAuthConnectionDao;
import com.cosfo.oms.tenant.dao.TenantPayConfigLogDao;
import com.cosfo.oms.tenant.model.dto.TenantAuthServiceDTO;
import com.cosfo.oms.tenant.model.po.TenantAuthConnection;
import com.cosfo.oms.tenant.model.po.TenantPayConfigLog;
import com.cosfo.oms.tenant.service.TenantPayConfigLogService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/8/23
 */
@Service
public class TenantPayConfigLogServiceImpl implements TenantPayConfigLogService {
    @Resource
    private TenantPayConfigLogDao tenantPayConfigLogDao;

    @Override
    public boolean saveTenantPayConfigLog(TenantAuthConnection tenantAuthConnection, TenantAuthServiceDTO dto, Long tenantAccountId) {
        TenantPayConfigLog tenantPayConfigLog = new TenantPayConfigLog();
        tenantPayConfigLog.setTenantId(dto.getTenantId());
        tenantPayConfigLog.setPayMchId(dto.getPayMchid());
        tenantPayConfigLog.setPaySecret(dto.getPaySecret());
        tenantPayConfigLog.setPayCertPath(dto.getPayCertPath());
        tenantPayConfigLog.setHuifuId(dto.getHuifuId());
        tenantPayConfigLog.setSecretKey(dto.getSecretKey());
        tenantPayConfigLog.setPublicKey(dto.getPublicKey());
        tenantPayConfigLog.setHuifuPublicKey(dto.getHuifuPublicKey());
        tenantPayConfigLog.setTenantAccountId(tenantAccountId);
        tenantPayConfigLog.setDinMerchantNo(dto.getDinMerchantNo());
        tenantPayConfigLog.setDinPrivateKey(dto.getDinPrivateKey());
        tenantPayConfigLog.setDinPublicKey(dto.getDinPublicKey());
        tenantPayConfigLog.setDinSecret(dto.getDinSecret());
        tenantPayConfigLogDao.save(tenantPayConfigLog);
        return true;
    }
}
