package com.cosfo.oms.wechat.controller;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.message.client.msgtemplate.req.MsgTemplateWechatPublicReq;
import com.cosfo.message.client.msgtemplate.req.WechatMsgTemplateReq;
import com.cosfo.message.client.msgtemplate.resp.WechatCreateAllTemplateResp;
import com.cosfo.message.client.msgtemplate.resp.WechatCreateTemplateResp;
import com.cosfo.oms.common.context.PrivacyVerEnum;
import com.cosfo.oms.common.result.ResultDTO;
import com.cosfo.oms.controller.BaseController;
import com.cosfo.oms.facade.MessageCenterServiceFacade;
import com.cosfo.oms.facade.convert.MsgTemplateConverter;
import com.cosfo.oms.msgscene.service.MsgSceneDomainService;
import com.cosfo.oms.wechat.model.dto.*;
import com.cosfo.oms.wechat.model.vo.*;
import com.cosfo.oms.wechat.service.AuthorizerService;
import com.cosfo.oms.wechat.service.WeixinTemplateService;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.CommonResult;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

/**
 * 小程序功能控制类
 */
@Slf4j
@RestController
public class WeiXinTemplateController extends BaseController {

    @Resource
    WeixinTemplateService weixinTemplateService;
    @Resource
    AuthorizerService authorizerService;
    @Resource
    private MessageCenterServiceFacade messageCenterServiceFacade;
    @Resource
    private MsgSceneDomainService msgSceneDomainService;

    /**
     * 发布代码
     * 获取待开发小程序用户列表
     * 通过
     */
    @RequestMapping(value = "/template/getDevelopTenants", method = RequestMethod.GET)
    public ResultDTO getDevelopTenants() {
        return ResultDTO.success(weixinTemplateService.getAuthTenants());
    }

    /**
     * 发布代码
     * 获取代码模板列表
     * 通过
     */
    @RequestMapping(value = "/template/getTemplateList", method = RequestMethod.GET)
    public ResultDTO getTemplateList() {
        return ResultDTO.success(weixinTemplateService.getTemplateList());
    }

    /**
     * 发布代码
     * 获取微信小程序草稿列表
     * 通过
     */
    @RequestMapping(value = "/template/draft/list", method = RequestMethod.GET)
    public ResultDTO getDraftList() {
        return ResultDTO.success(weixinTemplateService.getDraftList());
    }

    /**
     * 把草稿选为模板
     * 通过
     * 发布代码
     */
    @RequestMapping(value = "/template/addTemplate", method = RequestMethod.POST)
    public ResultDTO addTemplate(@RequestBody DraftVo draftVo) {
        return weixinTemplateService.addTpDraftToTemplate(draftVo.getDraftId());
    }

    /**
     * 删除模板
     * 通过
     * 发布代码
     */
    @DeleteMapping(value = "/template/delTemplate")
    public ResultDTO delTpTemplate(@RequestParam("templateId") String templateId) {
        return weixinTemplateService.delTpTemplate(templateId);
    }

    /**
     * 发布代码
     * 获取体验版二维码
     */
    @RequestMapping(value = "/template/getPreQrCode", method = RequestMethod.GET)
    public ResultDTO getPreQrCode(@RequestParam("appId") String appId,
                                  @RequestParam(value = "qrCodePath", required = false) String qrCodePath) throws IOException {
        return ResultDTO.success(weixinTemplateService.getTpQrCode(appId, qrCodePath));
    }

    /**
     * 上传小程序代码并生成体验版
     * 此接口不传appId为全量，传不是全量
     * 通过
     * 发布代码
     */
    @RequestMapping(value = "/template/commitCodeExperience", method = RequestMethod.POST)
    public ResultDTO commitCodeExperience(@RequestBody CommitCodePreVo commitCodePreVo) throws Exception {
        return weixinTemplateService.commitCodeExperience(commitCodePreVo);
    }

    /**
     * 发布代码
     * 设置小程序用户隐私保护指引
     */
    @RequestMapping(value = "/template/setPrivacySetting", method = RequestMethod.POST)
    public ResultDTO setPrivacySetting(@RequestBody PrivateSettingVo privateSettingVo) throws Exception {
        int ver = PrivacyVerEnum.DEV.getCode();
        if (!ObjectUtils.isEmpty(privateSettingVo) && privateSettingVo.getPrivacyVer() == PrivacyVerEnum.ONLINE.getCode()) {
            ver = PrivacyVerEnum.ONLINE.getCode();
        } else {
            ver = PrivacyVerEnum.DEV.getCode();
        }
        return weixinTemplateService.setPrivacySetting(privateSettingVo, ver);
    }

    /**
     * 提交审核接口，此接口传appId为根据appId进行提交，不传为全量状态为开发版本的提交审核
     * 通过
     * 发布代码
     */
    @RequestMapping(value = "/template/submitAuditPackage", method = RequestMethod.POST)
    public ResultDTO submitAuditPackage(@RequestBody CommitAuditVo commitAuditVo) throws Exception {
        return weixinTemplateService.submitAuditPackage(commitAuditVo);
    }

    /**
     * 批量设定订单页path信息接口
     * @return
     */
    @RequestMapping(value = "/template/set-order-path", method = RequestMethod.POST)
    public ResultDTO<?> applySetOrderPath(@RequestBody List<String> appIds) {
        return weixinTemplateService.applySetOrderPath(appIds);
    }

    /**
     * 发布代码
     * 发布已过审的小程序，此接口传appId为根据appId进行发布，不传为全量发布（全量发布需要全部小程序通过审核）
     * 1.更新原有审核成功的版本变为已发布，关联发布id
     * 2.更新前查询之前生成环境版本，如果存在上个生产环境版本则把生产环境版本设置为当前的上个版本
     * 3.更新当前的审核通过的版本为生产环境版本
     */
    @RequestMapping(value = "/template/releasePackage", method = RequestMethod.POST)
    public ResultDTO releasePackage(@RequestBody CommitAuditVo commitAuditVo) throws Exception {
        return weixinTemplateService.releasePackage(commitAuditVo);
    }

    /**
     * 发布代码
     * 指定appId为某个appId回退，不指定为全量版本回退
     */
    @RequestMapping(value = "/template/rollbackPackage", method = RequestMethod.POST)
    public ResultDTO rollbackPackage(@RequestBody CommitAuditVo commitAuditVo) throws Exception {
        return weixinTemplateService.rollbackPackage(commitAuditVo);
    }

    /**
     * 暂时没有用到，后续可能使用
     * 指定appId为某个appId审核撤回，不指定为全量审核撤回
     */
    @RequestMapping(value = "/template/withdrawPackage", method = RequestMethod.POST)
    public ResultDTO withdrawPackage(@RequestBody CommitAuditVo commitAuditVo) throws Exception {
        return weixinTemplateService.withdrawPackage(commitAuditVo);
    }


    /**
     * 暂时没有用到，后续可能使用
     * 初始化小程序域名
     */
    @GetMapping(value = "/system/initDomain")
    public ResultDTO initDomain(@RequestParam(value = "appId") String appId) throws Exception {
        return weixinTemplateService.initDomain(appId);
    }

    /**
     * 获取微信公共模板池列表
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/template-wechat/query/get-wechat-public-msg-template-list", method = RequestMethod.POST)
    public CommonResult<PageInfo<MsgPublicTemplateWechatDTO>> getWechatPublicMsgTemplateList(@RequestBody MsgTemplateWechatPublicVO msgTemplateWechatPublicVO) throws Exception{
        log.info("msgTemplateWechatPublicVO:{}", JSONObject.toJSONString(msgTemplateWechatPublicVO));
        MsgTemplateWechatPublicReq msgTemplateWechatPublicReq = MsgTemplateConverter.msgTemplateWechatPublicVO2Req(msgTemplateWechatPublicVO);
        PageInfo<MsgPublicTemplateWechatDTO> pageInfo = messageCenterServiceFacade.getWechatPublicMsgTemplateList(msgTemplateWechatPublicReq);
        return CommonResult.ok(pageInfo);
    }

    /**
     * 获取帆台模板池列表
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/template-wechat/query/get-wechat-msg-template-list", method = RequestMethod.POST)
    public CommonResult<PageInfo<MsgTemplateWechatDTO>> getWechatMsgTemplateList(@RequestBody MsgTemplateWechatVO msgTemplateWechatVO) throws Exception{
        WechatMsgTemplateReq wechatMsgTemplateReq = MsgTemplateConverter.wechatMsgTemplateVO2Req(msgTemplateWechatVO);
        PageInfo<MsgTemplateWechatDTO> pageInfo = messageCenterServiceFacade.getWechatMsgTemplateList(wechatMsgTemplateReq);
        return CommonResult.ok(pageInfo);
    }
    /**
     * 获取帆台场景列表
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/template-wechat/query/get-wechat-msg-scene-list", method = RequestMethod.POST)
    public CommonResult<PageInfo<MsgSceneDTO>> getWechatMsgSceneList(@RequestBody MsgSceneWechatVO msgSceneWechatVO) throws Exception{
        PageInfo<MsgSceneDTO> pageInfo = msgSceneDomainService.getWechatMsgSceneList(msgSceneWechatVO);
        return CommonResult.ok(pageInfo);
    }


    /**
     * 获取帆台模板关联场景明细
     * 点击已关联场景数显示
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/template-wechat/query/get-wechat-msg-scene-template-detail", method = RequestMethod.POST)
    public CommonResult<MsgSceneWechatDetailDTO> getWechatMsgSceneTemplateDetail(@RequestBody IdParamVO idParamVO) throws Exception{
        MsgSceneWechatDetailDTO detail = msgSceneDomainService.getWechatMsgSceneTemplateDetail(idParamVO.getId());
        return CommonResult.ok(detail);
    }

    /**
     * 获取帆台模板详情
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/template-wechat/query/get-wechat-msg-template-detail", method = RequestMethod.POST)
    public CommonResult<MsgSceneWechatDetailDTO> getWechatMsgTemplateDetail(@RequestBody IdParamVO idParamVO) throws Exception{
        MsgSceneWechatDetailDTO templateDetail = messageCenterServiceFacade.getWechatMsgTemplateDetail(idParamVO.getId());
        return CommonResult.ok(templateDetail);
    }

    /**
     * 获取帆台模板未关联、已关联、创建失败小程序信息
     * @param msgWechatAppVO
     * 入参中type 0未关联、1已关联
     * successFlag 0创建失败 1成功
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/template-wechat/query/get-wechat-app-list", method = RequestMethod.POST)
    public CommonResult<PageInfo<MsgTemplateWechatDTO>> getWechatAppList(@RequestBody MsgWechatAppVO msgWechatAppVO) throws Exception{
        PageInfo<MsgTemplateWechatDTO> pageInfo = messageCenterServiceFacade.getWechatAppList(MsgTemplateConverter.msgWechatAppVO2Req(msgWechatAppVO));
        return CommonResult.ok(pageInfo);
    }
    /**
     * 按模板id获取小程序信息
     * 获取场景详情
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/template-wechat/query/get-wechat-app-info-by-id", method = RequestMethod.POST)
    public CommonResult<MsgTemplateWechatDTO> getWechatAppInfoById(@RequestBody IdParamVO idParamVO) throws Exception{
        MsgTemplateWechatDTO wechatDTO = messageCenterServiceFacade.getWechatAppInfoById(idParamVO.getId());
        return CommonResult.ok(wechatDTO);
    }

    /**
     * 获取模板关键词
     * @param idParamVO 模板id
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/template-wechat/query/get-template-keyword-list", method = RequestMethod.POST)
    public CommonResult getTemplateKeyWords(@RequestBody IdParamVO idParamVO) throws Exception{
        return CommonResult.ok();
    }

    /**
     * 未关联小程序全部创建模板
     * @param templateWechatVO
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/template-wechat/add/create-all-template-list", method = RequestMethod.POST)
    public CommonResult<WechatCreateAllTemplateDTO> createAllTemplateList(@RequestBody PrivateMsgTemplateWechatVO templateWechatVO) throws Exception{
        templateWechatVO.setUId(getMerchantInfoDTO().getId());
        WechatCreateAllTemplateResp wechatCreateAllTemplateResp = messageCenterServiceFacade.createAllTemplateList(MsgTemplateConverter.privateMsgTemplateWechatVO2Req(templateWechatVO));
        WechatCreateAllTemplateDTO wechatCreateAllTemplateDTO = MsgTemplateConverter.wechatCreateAllTemplateResp2DTO(wechatCreateAllTemplateResp);
        return CommonResult.ok(wechatCreateAllTemplateDTO);
    }

    /**
     * 未关联小程序(商户)单独创建帆台模板
     * @param templateWechatVO
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/template-wechat/add/create-one-template", method = RequestMethod.POST)
    public CommonResult<Long> createOneTemplate(@RequestBody PrivateMsgTemplateWechatVO templateWechatVO) throws Exception{
        templateWechatVO.setUId(getMerchantInfoDTO().getId());
        Long templateId = messageCenterServiceFacade.createOneTemplate(MsgTemplateConverter.privateMsgTemplateWechatVO2Req(templateWechatVO));
        return CommonResult.ok(templateId);
    }

    /**
     * 获取帆台场景详情
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/template-wechat/query/get-wechat-msg-scene-detail", method = RequestMethod.POST)
    public CommonResult<MsgSceneDTO> getWechatMsgSceneDetail(@RequestBody IdParamVO idParamVO) throws Exception{
        MsgSceneDTO sceneDetail = msgSceneDomainService.getWechatMsgSceneDetail(idParamVO.getId());
        return CommonResult.ok(sceneDetail);
    }

    /**
     * 帆台场景更新方法,帆台维度
     * @param msgSceneWechatVO
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/template-wechat/update/update-scene", method = RequestMethod.POST)
    public CommonResult updateScene(@RequestBody MsgSceneWechatVO msgSceneWechatVO) throws Exception{
        msgSceneWechatVO.setUpdater(getMerchantInfoDTO().getId());
        msgSceneDomainService.updateScene(msgSceneWechatVO);
        return CommonResult.ok();
    }


    /**
     * 获取消息场景总数量和已启用数量
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/template-wechat/query/get-wechat-msg-scene-count", method = RequestMethod.POST)
    public CommonResult<MsgSceneCountDTO> getWechatMsgSceneCount(@RequestBody IdParamVO idParamVO) throws Exception{
        MsgSceneCountDTO sceneCount = msgSceneDomainService.getWechatMsgSceneCount(idParamVO.getId());
        return CommonResult.ok(sceneCount);
    }

    /**
     * 客户维度的启用/禁用
     * @param msgSceneUserWechatVO
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/template-wechat/update/update-scene-by-user", method = RequestMethod.POST)
    public CommonResult updateSceneByUser(@RequestBody MsgSceneUserWechatVO msgSceneUserWechatVO) throws Exception{
        msgSceneUserWechatVO.setUId(getMerchantInfoDTO().getId());
        msgSceneDomainService.updateSceneByUser(msgSceneUserWechatVO);
        return CommonResult.ok();
    }

    /**
     * 获取客户场景列表
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/template-wechat/query/get-user-wechat-msg-scene-list", method = RequestMethod.POST)
    public CommonResult<PageInfo<MsgUserSceneDTO>> getUserWechatMsgSceneList(@RequestBody MsgSceneUserWechatVO msgSceneUserWechatVO) throws Exception{
        PageInfo<MsgUserSceneDTO> pageInfo = msgSceneDomainService.getUserWechatMsgSceneList(msgSceneUserWechatVO);
        return CommonResult.ok(pageInfo);
    }

    /**
     * 创建失败小程序重试接口
     * @param retryTemplateVO
     * @return
     */
    @RequestMapping(value = "/template-wechat/add/retry-one-template", method = RequestMethod.POST)
    public CommonResult<WechatCreateTemplateDTO> createTemplateRetry(@RequestBody RetryTemplateVO retryTemplateVO){
        WechatCreateTemplateResp templateRetry = messageCenterServiceFacade.createTemplateRetry(retryTemplateVO.getId(), retryTemplateVO.getTenantId());
        return CommonResult.ok(MsgTemplateConverter.wechatCreateTemplateResp2DTO(templateRetry));
    }

    /**
     * 按模板id和tenantId刷新模板状态
     * @param retryTemplateVO
     * @return
     */
    @RequestMapping(value = "/template-wechat/add/refresh-template", method = RequestMethod.POST)
    public CommonResult refreshTemplate(@RequestBody RetryTemplateVO retryTemplateVO){
        messageCenterServiceFacade.refreshTemplate(retryTemplateVO);
        return CommonResult.ok();
    }

    /**
     * 创建微信模板
     * @param templateWechatVO
     * @return
     */
    @RequestMapping(value = "/template-wechat/add/add-wechat-template", method = RequestMethod.POST)
    public CommonResult<WechatCreateTemplateDTO> createOneWeiXinTemplate(@RequestBody PrivateMsgTemplateWechatVO templateWechatVO){
        templateWechatVO.setUId(getMerchantInfoDTO().getId());
        WechatCreateTemplateResp oneWeiXinTemplate = messageCenterServiceFacade.createOneWeiXinTemplate(MsgTemplateConverter.privateMsgTemplateWechatVO2Req(templateWechatVO));
        return CommonResult.ok(MsgTemplateConverter.wechatCreateTemplateResp2DTO(oneWeiXinTemplate));
    }


}
