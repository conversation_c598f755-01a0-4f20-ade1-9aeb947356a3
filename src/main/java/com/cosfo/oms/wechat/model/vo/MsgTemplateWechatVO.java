package com.cosfo.oms.wechat.model.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 微信消息模版VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-31
 */
@Data
public class MsgTemplateWechatVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key（模板id）
     */
    private Long id;


    /**
     * 微信模版名称(标题)
     */
    private String wechatTitle;

    /**
     * 开始日期
     */
    private LocalDateTime startTime;

    /**
     * 结束日期
     */
    private LocalDateTime endTime;


    /**
     * 当前页码
     */
    private Integer pageIndex;

    /**
     * 页面大小
     */
    private Integer pageSize;


}
