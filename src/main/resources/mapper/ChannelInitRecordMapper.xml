<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.oms.tenant.mapper.ChannelInitRecordMapper">
    
    <resultMap id="BaseResultMap" type="com.cosfo.oms.tenant.model.po.ChannelInitRecord">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
        <result column="auth_connection_id" jdbcType="BIGINT" property="authConnectionId" />
        <result column="channel_type" jdbcType="VARCHAR" property="channelType" />
        <result column="channel_id" jdbcType="BIGINT" property="channelId" />
        <result column="init_status" jdbcType="TINYINT" property="initStatus" />
        <result column="init_time" jdbcType="TIMESTAMP" property="initTime" />
        <result column="error_message" jdbcType="LONGVARCHAR" property="errorMessage" />
        <result column="retry_count" jdbcType="INTEGER" property="retryCount" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>
    
    <sql id="Base_Column_List">
        id, tenant_id, auth_connection_id, channel_type, channel_id, init_status, 
        init_time, error_message, retry_count, create_time, update_time
    </sql>
    
    <insert id="insert" parameterType="com.cosfo.oms.tenant.model.po.ChannelInitRecord" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO channel_init_record (
            tenant_id, auth_connection_id, channel_type, channel_id, init_status,
            init_time, error_message, retry_count, create_time, update_time
        ) VALUES (
            #{tenantId,jdbcType=BIGINT}, #{authConnectionId,jdbcType=BIGINT}, 
            #{channelType,jdbcType=VARCHAR}, #{channelId,jdbcType=BIGINT}, 
            #{initStatus,jdbcType=TINYINT}, #{initTime,jdbcType=TIMESTAMP}, 
            #{errorMessage,jdbcType=LONGVARCHAR}, #{retryCount,jdbcType=INTEGER}, 
            NOW(), NOW()
        )
    </insert>
    
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO channel_init_record (
            tenant_id, auth_connection_id, channel_type, channel_id, init_status,
            init_time, error_message, retry_count, create_time, update_time
        ) VALUES
        <foreach collection="records" item="record" separator=",">
            (#{record.tenantId,jdbcType=BIGINT}, #{record.authConnectionId,jdbcType=BIGINT}, 
             #{record.channelType,jdbcType=VARCHAR}, #{record.channelId,jdbcType=BIGINT}, 
             #{record.initStatus,jdbcType=TINYINT}, #{record.initTime,jdbcType=TIMESTAMP}, 
             #{record.errorMessage,jdbcType=LONGVARCHAR}, #{record.retryCount,jdbcType=INTEGER}, 
             NOW(), NOW())
        </foreach>
    </insert>
    
    <update id="updateByPrimaryKey" parameterType="com.cosfo.oms.tenant.model.po.ChannelInitRecord">
        UPDATE channel_init_record
        SET tenant_id = #{tenantId,jdbcType=BIGINT},
            auth_connection_id = #{authConnectionId,jdbcType=BIGINT},
            channel_type = #{channelType,jdbcType=VARCHAR},
            channel_id = #{channelId,jdbcType=BIGINT},
            init_status = #{initStatus,jdbcType=TINYINT},
            init_time = #{initTime,jdbcType=TIMESTAMP},
            error_message = #{errorMessage,jdbcType=LONGVARCHAR},
            retry_count = #{retryCount,jdbcType=INTEGER},
            update_time = NOW()
        WHERE id = #{id,jdbcType=BIGINT}
    </update>
    
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM channel_init_record
        WHERE id = #{id,jdbcType=BIGINT}
    </select>
    
    <select id="selectByTenantAndChannelType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM channel_init_record
        WHERE tenant_id = #{tenantId,jdbcType=BIGINT}
          AND auth_connection_id = #{authConnectionId,jdbcType=BIGINT}
          AND channel_type = #{channelType,jdbcType=VARCHAR}
    </select>
    
    <select id="selectByTenantId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM channel_init_record
        WHERE tenant_id = #{tenantId,jdbcType=BIGINT}
        ORDER BY create_time DESC
    </select>
    
    <select id="selectByInitStatus" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM channel_init_record
        WHERE init_status = #{initStatus,jdbcType=TINYINT}
        ORDER BY create_time ASC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>
    
    <select id="countByTenantAndStatus" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM channel_init_record
        WHERE 1=1
        <if test="tenantId != null">
            AND tenant_id = #{tenantId,jdbcType=BIGINT}
        </if>
        <if test="initStatus != null">
            AND init_status = #{initStatus,jdbcType=TINYINT}
        </if>
    </select>
    
</mapper>
