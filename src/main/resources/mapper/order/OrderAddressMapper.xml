<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.oms.order.mapper.OrderAddressMapper">
    <resultMap id="BaseResultMap" type="com.cosfo.oms.order.model.po.OrderAddress">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="contact_name" jdbcType="VARCHAR" property="contactName"/>
        <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone"/>
        <result column="province" jdbcType="VARCHAR" property="province"/>
        <result column="city" jdbcType="VARCHAR" property="city"/>
        <result column="area" jdbcType="VARCHAR" property="area"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="poi_note" jdbcType="VARCHAR" property="poiNote" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, tenant_id, order_id, contact_name, contact_phone, province, city, area, address, poi_note,
        create_time, update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from order_address
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from order_address
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.oms.order.model.po.OrderAddress"
            useGeneratedKeys="true">
        insert into order_address (tenant_id, order_id, contact_name,
        contact_phone, province, city,
        area, address, create_time,
        update_time)
        values (#{tenantId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, #{contactName,jdbcType=VARCHAR},
        #{contactPhone,jdbcType=VARCHAR}, #{province,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR},
        #{area,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.cosfo.oms.order.model.po.OrderAddress" useGeneratedKeys="true">
        insert into order_address
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="orderId != null">
                order_id,
            </if>
            <if test="contactName != null">
                contact_name,
            </if>
            <if test="contactPhone != null">
                contact_phone,
            </if>
            <if test="province != null">
                province,
            </if>
            <if test="city != null">
                city,
            </if>
            <if test="area != null">
                area,
            </if>
            <if test="address != null">
                address,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tenantId != null">
                #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="orderId != null">
                #{orderId,jdbcType=BIGINT},
            </if>
            <if test="contactName != null">
                #{contactName,jdbcType=VARCHAR},
            </if>
            <if test="contactPhone != null">
                #{contactPhone,jdbcType=VARCHAR},
            </if>
            <if test="province != null">
                #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                #{city,jdbcType=VARCHAR},
            </if>
            <if test="area != null">
                #{area,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.oms.order.model.po.OrderAddress">
        update order_address
        <set>
            <if test="tenantId != null">
                tenant_id = #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="orderId != null">
                order_id = #{orderId,jdbcType=BIGINT},
            </if>
            <if test="contactName != null">
                contact_name = #{contactName,jdbcType=VARCHAR},
            </if>
            <if test="contactPhone != null">
                contact_phone = #{contactPhone,jdbcType=VARCHAR},
            </if>
            <if test="province != null">
                province = #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                city = #{city,jdbcType=VARCHAR},
            </if>
            <if test="area != null">
                area = #{area,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.cosfo.oms.order.model.po.OrderAddress">
        update order_address
        set tenant_id = #{tenantId,jdbcType=BIGINT},
        order_id = #{orderId,jdbcType=BIGINT},
        contact_name = #{contactName,jdbcType=VARCHAR},
        contact_phone = #{contactPhone,jdbcType=VARCHAR},
        province = #{province,jdbcType=VARCHAR},
        city = #{city,jdbcType=VARCHAR},
        area = #{area,jdbcType=VARCHAR},
        address = #{address,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectByOrderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from order_address
        where order_id = #{orderId}
        and tenant_id = #{tenantId}
    </select>

    <select id="selectByOrderIds" resultType="com.cosfo.oms.order.model.po.OrderAddress">
        select
        <include refid="Base_Column_List"/>
        from order_address
        where order_id IN
        <foreach collection="orderIds" open="(" close=")" separator="," item="orderId">
            #{orderId}
        </foreach>
    </select>
</mapper>
