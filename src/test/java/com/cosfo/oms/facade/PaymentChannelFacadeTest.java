package com.cosfo.oms.facade;

import com.cosfo.oms.common.context.IndirectChannelTypeEnum;
import com.cosfo.oms.common.context.PaymentMethodSwitchEnum;
import com.cosfo.oms.tenant.model.dto.TenantAuthServiceDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import net.summerfarm.client.provider.payment.PaymentChannelProvider;
import net.summerfarm.client.resp.payment.PaymentChannelSaveResp;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * PaymentChannelFacade 单元测试
 */
@ExtendWith(MockitoExtension.class)
class PaymentChannelFacadeTest {

    @Mock
    private PaymentChannelProvider paymentChannelProvider;

    @InjectMocks
    private PaymentChannelFacade paymentChannelFacade;

    private TenantAuthServiceDTO testDto;

    @BeforeEach
    void setUp() {
        testDto = new TenantAuthServiceDTO();
        testDto.setTenantId(12345L);
    }

    @Test
    void testSaveMultiChannelConfigs_WechatDirectOnly() {
        // 准备测试数据 - 只启用微信直连
        testDto.setWechatDirectSwitch(PaymentMethodSwitchEnum.ENABLED.getCode());
        testDto.setPayMchid("wx_merchant_123");
        testDto.setPaySecret("wx_secret_123");
        testDto.setPayCertPath("/path/to/cert");
        testDto.setAppletWechatPaySwitch(PaymentMethodSwitchEnum.ENABLED.getCode());

        // 其他渠道关闭
        testDto.setWechatIndirectSwitch(PaymentMethodSwitchEnum.DISABLED.getCode());
        testDto.setWechatIndirectPluginSwitch(PaymentMethodSwitchEnum.DISABLED.getCode());
        testDto.setH5WechatIndirectSwitch(PaymentMethodSwitchEnum.DISABLED.getCode());
        testDto.setAliIndirectSwitch(PaymentMethodSwitchEnum.DISABLED.getCode());

        // Mock 中央服务响应
        PaymentChannelSaveResp mockResp = new PaymentChannelSaveResp();
        mockResp.setChannelId(1001L);
        when(paymentChannelProvider.saveChannel(any())).thenReturn(mockResp);

        // 执行测试
        List<Long> result = paymentChannelFacade.saveMultiChannelConfigs(testDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(1001L, result.get(0));
    }

    @Test
    void testSaveMultiChannelConfigs_IndirectChannelOnly() {
        // 准备测试数据 - 只启用间连渠道（汇付）
        testDto.setWechatDirectSwitch(PaymentMethodSwitchEnum.DISABLED.getCode());
        testDto.setWechatIndirectSwitch(PaymentMethodSwitchEnum.ENABLED.getCode());
        testDto.setIndirectOnlineChannel(IndirectChannelTypeEnum.HUI_FU.getCode());
        testDto.setHuifuId("huifu_merchant_123");
        testDto.setSecretKey("huifu_private_key");
        testDto.setHuifuPublicKey("huifu_public_key");
        testDto.setAppletWechatPaySwitch(PaymentMethodSwitchEnum.ENABLED.getCode());

        // 其他渠道关闭
        testDto.setWechatIndirectPluginSwitch(PaymentMethodSwitchEnum.DISABLED.getCode());
        testDto.setH5WechatIndirectSwitch(PaymentMethodSwitchEnum.DISABLED.getCode());
        testDto.setAliIndirectSwitch(PaymentMethodSwitchEnum.DISABLED.getCode());

        // Mock 中央服务响应
        PaymentChannelSaveResp mockResp = new PaymentChannelSaveResp();
        mockResp.setChannelId(1002L);
        when(paymentChannelProvider.saveChannel(any())).thenReturn(mockResp);

        // 执行测试
        List<Long> result = paymentChannelFacade.saveMultiChannelConfigs(testDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(1002L, result.get(0));
    }

    @Test
    void testSaveMultiChannelConfigs_BothChannels() {
        // 准备测试数据 - 同时启用微信直连和间连渠道
        testDto.setWechatDirectSwitch(PaymentMethodSwitchEnum.ENABLED.getCode());
        testDto.setPayMchid("wx_merchant_123");
        testDto.setPaySecret("wx_secret_123");
        testDto.setPayCertPath("/path/to/cert");

        testDto.setWechatIndirectSwitch(PaymentMethodSwitchEnum.ENABLED.getCode());
        testDto.setIndirectOnlineChannel(IndirectChannelTypeEnum.HUI_FU.getCode());
        testDto.setHuifuId("huifu_merchant_123");
        testDto.setSecretKey("huifu_private_key");
        testDto.setHuifuPublicKey("huifu_public_key");

        testDto.setAppletWechatPaySwitch(PaymentMethodSwitchEnum.ENABLED.getCode());

        // Mock 中央服务响应
        PaymentChannelSaveResp mockResp1 = new PaymentChannelSaveResp();
        mockResp1.setChannelId(1001L);
        PaymentChannelSaveResp mockResp2 = new PaymentChannelSaveResp();
        mockResp2.setChannelId(1002L);
        when(paymentChannelProvider.saveChannel(any()))
            .thenReturn(mockResp1)
            .thenReturn(mockResp2);

        // 执行测试
        List<Long> result = paymentChannelFacade.saveMultiChannelConfigs(testDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains(1001L));
        assertTrue(result.contains(1002L));
    }

    @Test
    void testSaveMultiChannelConfigs_NoChannelsEnabled() {
        // 准备测试数据 - 没有启用任何渠道
        testDto.setWechatDirectSwitch(PaymentMethodSwitchEnum.DISABLED.getCode());
        testDto.setWechatIndirectSwitch(PaymentMethodSwitchEnum.DISABLED.getCode());
        testDto.setWechatIndirectPluginSwitch(PaymentMethodSwitchEnum.DISABLED.getCode());
        testDto.setH5WechatIndirectSwitch(PaymentMethodSwitchEnum.DISABLED.getCode());
        testDto.setAliIndirectSwitch(PaymentMethodSwitchEnum.DISABLED.getCode());

        // 执行测试
        List<Long> result = paymentChannelFacade.saveMultiChannelConfigs(testDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.size());
    }
}
