package com.cosfo.oms.schedulerx.task;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import net.xianmu.task.vo.input.XmJobInput;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

/**
 * 渠道初始化SchedulerX任务测试
 *
 * <AUTHOR>
 * @date 2025-08-18
 */
@ActiveProfiles("dev")
@SpringBootTest
class ChannelInitSchedulerXTaskTest {

    @Resource
    private ChannelInitSchedulerXTask channelInitSchedulerXTask;

    @Test
    void testProcessResult_AllTenants() throws Exception {
        // 测试处理全部租户
        XmJobInput jobInput = new XmJobInput();
        jobInput.setJobId(1001L);
        jobInput.setInstanceParameters("{}");

        ProcessResult result = channelInitSchedulerXTask.processResult(jobInput);

        System.out.println("处理结果: " + result.isSuccess());
        System.out.println("结果消息: " + result.getResult());
    }

    @Test
    void testProcessResult_SpecificTenants() throws Exception {
        // 测试处理指定租户
        XmJobInput jobInput = new XmJobInput();
        jobInput.setJobId(1002L);
        jobInput.setInstanceParameters("{\"tenantIds\": \"12345,67890\"}");

        ProcessResult result = channelInitSchedulerXTask.processResult(jobInput);

        System.out.println("处理结果: " + result.isSuccess());
        System.out.println("结果消息: " + result.getResult());
    }

    @Test
    void testProcessResult_CustomBatchSize() throws Exception {
        // 测试自定义批处理大小
        XmJobInput jobInput = new XmJobInput();
        jobInput.setJobId(1003L);
        jobInput.setInstanceParameters("{\"tenantIds\": \"12345,67890,11111\", \"batchSize\": 10}");

        ProcessResult result = channelInitSchedulerXTask.processResult(jobInput);

        System.out.println("处理结果: " + result.isSuccess());
        System.out.println("结果消息: " + result.getResult());
    }

    @Test
    void testProcessResult_InvalidParameters() throws Exception {
        // 测试无效参数
        XmJobInput jobInput = new XmJobInput();
        jobInput.setJobId(1004L);
        jobInput.setInstanceParameters("invalid json");

        ProcessResult result = channelInitSchedulerXTask.processResult(jobInput);

        System.out.println("处理结果: " + result.isSuccess());
        System.out.println("结果消息: " + result.getResult());
    }

    @Test
    void testProcessResult_EmptyParameters() throws Exception {
        // 测试空参数
        XmJobInput jobInput = new XmJobInput();
        jobInput.setJobId(1005L);
        jobInput.setInstanceParameters("");

        ProcessResult result = channelInitSchedulerXTask.processResult(jobInput);

        System.out.println("处理结果: " + result.isSuccess());
        System.out.println("结果消息: " + result.getResult());
    }

    @Test
    void testProcessResult_SingleTenant() throws Exception {
        // 测试单个租户
        XmJobInput jobInput = new XmJobInput();
        jobInput.setJobId(1006L);
        jobInput.setInstanceParameters("{\"tenantIds\": \"12345\"}");

        ProcessResult result = channelInitSchedulerXTask.processResult(jobInput);

        System.out.println("处理结果: " + result.isSuccess());
        System.out.println("结果消息: " + result.getResult());
    }

    @Test
    void testProcessResult_InvalidTenantIds() throws Exception {
        // 测试无效的租户ID
        XmJobInput jobInput = new XmJobInput();
        jobInput.setJobId(1007L);
        jobInput.setInstanceParameters("{\"tenantIds\": \"12345,abc,67890,xyz\"}");

        ProcessResult result = channelInitSchedulerXTask.processResult(jobInput);

        System.out.println("处理结果: " + result.isSuccess());
        System.out.println("结果消息: " + result.getResult());
    }
}
