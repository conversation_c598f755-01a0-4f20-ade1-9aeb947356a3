package com.cosfo.oms.tenant.service.impl;

import com.cosfo.oms.common.context.IndirectChannelTypeEnum;
import com.cosfo.oms.common.context.PaymentMethodSwitchEnum;
import com.cosfo.oms.facade.payment.PaymentChannelFacade;
import com.cosfo.oms.model.dto.LoginContextInfoDTO;
import com.cosfo.oms.tenant.dao.TenantAuthConnectionDao;
import com.cosfo.oms.tenant.model.dto.TenantAuthServiceDTO;
import com.cosfo.oms.tenant.model.po.TenantAuthConnection;
import com.cosfo.oms.tenant.service.TenantInfoService;
import net.xianmu.usercenter.client.tenant.resp.TenantResultResp;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

/**
 * TenantAuthConnectionServiceImpl 单元测试
 */
@ExtendWith(MockitoExtension.class)
class TenantAuthConnectionServiceImplTest {

    @Mock
    private TenantAuthConnectionDao tenantAuthConnectionDao;

    @Mock
    private TenantInfoService tenantInfoService;

    @Mock
    private PaymentChannelFacade paymentChannelFacade;

    @InjectMocks
    private TenantAuthConnectionServiceImpl tenantAuthConnectionService;

    private TenantAuthServiceDTO testDto;
    private LoginContextInfoDTO loginContextInfoDTO;

    @BeforeEach
    void setUp() {
        testDto = new TenantAuthServiceDTO();
        testDto.setTenantId(12345L);
        
        loginContextInfoDTO = new LoginContextInfoDTO();
        loginContextInfoDTO.setAuthUserId(1001L);
    }

    @Test
    void testSaveOrUpdateAuth_Success_WechatDirectOnly() {
        // 准备测试数据 - 只启用微信直连
        testDto.setWechatDirectSwitch(PaymentMethodSwitchEnum.ENABLED.getCode());
        testDto.setPayMchid("wx_merchant_123");
        testDto.setPaySecret("wx_secret_123");
        testDto.setPayCertPath("/path/to/cert");
        testDto.setAppletWechatPaySwitch(PaymentMethodSwitchEnum.ENABLED.getCode());

        // 其他渠道关闭
        testDto.setWechatIndirectSwitch(PaymentMethodSwitchEnum.DISABLED.getCode());
        testDto.setWechatIndirectPluginSwitch(PaymentMethodSwitchEnum.DISABLED.getCode());
        testDto.setH5WechatIndirectSwitch(PaymentMethodSwitchEnum.DISABLED.getCode());
        testDto.setAliIndirectSwitch(PaymentMethodSwitchEnum.DISABLED.getCode());

        // Mock 依赖服务
        TenantResultResp tenantResultResp = new TenantResultResp();
        tenantResultResp.setId(12345L);
        when(tenantInfoService.getTenantInfo(anyLong())).thenReturn(tenantResultResp);
        
        when(tenantAuthConnectionDao.listByParam(any())).thenReturn(new ArrayList<>());
        when(paymentChannelFacade.saveMultiChannelConfigs(any())).thenReturn(Arrays.asList(1001L));
        doNothing().when(tenantAuthConnectionDao).saveOrUpdateTenantAuthConn(any());

        // 执行测试
        assertDoesNotThrow(() -> {
            tenantAuthConnectionService.saveOrUpdateAuth(testDto, loginContextInfoDTO);
        });

        // 验证调用
        verify(tenantInfoService, times(1)).getTenantInfo(12345L);
        verify(paymentChannelFacade, times(1)).saveMultiChannelConfigs(testDto);
        verify(tenantAuthConnectionDao, times(1)).saveOrUpdateTenantAuthConn(testDto);
    }

    @Test
    void testSaveOrUpdateAuth_Success_IndirectChannelOnly() {
        // 准备测试数据 - 只启用间连渠道（汇付）
        testDto.setWechatDirectSwitch(PaymentMethodSwitchEnum.DISABLED.getCode());
        testDto.setWechatIndirectSwitch(PaymentMethodSwitchEnum.ENABLED.getCode());
        testDto.setIndirectOnlineChannel(IndirectChannelTypeEnum.HUI_FU.getCode());
        testDto.setHuifuId("huifu_merchant_123");
        testDto.setSecretKey("huifu_private_key");
        testDto.setHuifuPublicKey("huifu_public_key");
        testDto.setAppletWechatPaySwitch(PaymentMethodSwitchEnum.ENABLED.getCode());

        // 其他渠道关闭
        testDto.setWechatIndirectPluginSwitch(PaymentMethodSwitchEnum.DISABLED.getCode());
        testDto.setH5WechatIndirectSwitch(PaymentMethodSwitchEnum.DISABLED.getCode());
        testDto.setAliIndirectSwitch(PaymentMethodSwitchEnum.DISABLED.getCode());

        // Mock 依赖服务
        TenantResultResp tenantResultResp = new TenantResultResp();
        tenantResultResp.setId(12345L);
        when(tenantInfoService.getTenantInfo(anyLong())).thenReturn(tenantResultResp);
        
        when(tenantAuthConnectionDao.listByParam(any())).thenReturn(new ArrayList<>());
        when(paymentChannelFacade.saveMultiChannelConfigs(any())).thenReturn(Arrays.asList(1002L));
        doNothing().when(tenantAuthConnectionDao).saveOrUpdateTenantAuthConn(any());

        // 执行测试
        assertDoesNotThrow(() -> {
            tenantAuthConnectionService.saveOrUpdateAuth(testDto, loginContextInfoDTO);
        });

        // 验证调用
        verify(tenantInfoService, times(1)).getTenantInfo(12345L);
        verify(paymentChannelFacade, times(1)).saveMultiChannelConfigs(testDto);
        verify(tenantAuthConnectionDao, times(1)).saveOrUpdateTenantAuthConn(testDto);
    }

    @Test
    void testSaveOrUpdateAuth_Success_BothChannels() {
        // 准备测试数据 - 同时启用微信直连和间连渠道
        testDto.setWechatDirectSwitch(PaymentMethodSwitchEnum.ENABLED.getCode());
        testDto.setPayMchid("wx_merchant_123");
        testDto.setPaySecret("wx_secret_123");
        testDto.setPayCertPath("/path/to/cert");

        testDto.setWechatIndirectSwitch(PaymentMethodSwitchEnum.ENABLED.getCode());
        testDto.setIndirectOnlineChannel(IndirectChannelTypeEnum.HUI_FU.getCode());
        testDto.setHuifuId("huifu_merchant_123");
        testDto.setSecretKey("huifu_private_key");
        testDto.setHuifuPublicKey("huifu_public_key");

        testDto.setAppletWechatPaySwitch(PaymentMethodSwitchEnum.ENABLED.getCode());

        // Mock 依赖服务
        TenantResultResp tenantResultResp = new TenantResultResp();
        tenantResultResp.setId(12345L);
        when(tenantInfoService.getTenantInfo(anyLong())).thenReturn(tenantResultResp);
        
        when(tenantAuthConnectionDao.listByParam(any())).thenReturn(new ArrayList<>());
        when(paymentChannelFacade.saveMultiChannelConfigs(any())).thenReturn(Arrays.asList(1001L, 1002L));
        doNothing().when(tenantAuthConnectionDao).saveOrUpdateTenantAuthConn(any());

        // 执行测试
        assertDoesNotThrow(() -> {
            tenantAuthConnectionService.saveOrUpdateAuth(testDto, loginContextInfoDTO);
        });

        // 验证调用
        verify(tenantInfoService, times(1)).getTenantInfo(12345L);
        verify(paymentChannelFacade, times(1)).saveMultiChannelConfigs(testDto);
        verify(tenantAuthConnectionDao, times(1)).saveOrUpdateTenantAuthConn(testDto);
    }

    @Test
    void testSaveOrUpdateAuth_WithExistingConnection() {
        // 准备测试数据
        testDto.setWechatDirectSwitch(PaymentMethodSwitchEnum.ENABLED.getCode());
        testDto.setPayMchid("wx_merchant_123");
        testDto.setPaySecret("wx_secret_123");
        testDto.setPayCertPath("/path/to/cert");
        testDto.setAppletWechatPaySwitch(PaymentMethodSwitchEnum.ENABLED.getCode());

        // Mock 现有连接
        TenantAuthConnection existingConnection = new TenantAuthConnection();
        existingConnection.setId(999L);
        existingConnection.setTenantId(12345L);

        // Mock 依赖服务
        TenantResultResp tenantResultResp = new TenantResultResp();
        tenantResultResp.setId(12345L);
        when(tenantInfoService.getTenantInfo(anyLong())).thenReturn(tenantResultResp);
        
        when(tenantAuthConnectionDao.listByParam(any())).thenReturn(Arrays.asList(existingConnection));
        when(paymentChannelFacade.saveMultiChannelConfigs(any())).thenReturn(Arrays.asList(1001L));
        doNothing().when(tenantAuthConnectionDao).saveOrUpdateTenantAuthConn(any());

        // 执行测试
        assertDoesNotThrow(() -> {
            tenantAuthConnectionService.saveOrUpdateAuth(testDto, loginContextInfoDTO);
        });

        // 验证 ID 被设置
        assertEquals(999L, testDto.getId());
        
        // 验证调用
        verify(tenantInfoService, times(1)).getTenantInfo(12345L);
        verify(paymentChannelFacade, times(1)).saveMultiChannelConfigs(testDto);
        verify(tenantAuthConnectionDao, times(1)).saveOrUpdateTenantAuthConn(testDto);
    }
}
